import React, { useState, useEffect } from 'react';
import {
  ChevronDown,
  ChevronUp,
  ExternalLink,
  Play,
  BookOpen,
  Image as ImageIcon,
  Calculator,
  Lightbulb,
  Target,
  Star,
  Clock,
  Eye,
  Link,
  FileText,
  CheckCircle,
  X
} from 'lucide-react';
import { AIResponse as AIResponseType } from '../../types/askAI';
import MarkdownRenderer from '../common/MarkdownRenderer';
import MathRenderer from '../common/MathRenderer';
import { youtubeService } from '../../lib/youtubeService';

interface AIResponseProps {
  response: AIResponseType;
}

interface ImageCardProps {
  image: any;
  index: number;
}

interface VideoModalProps {
  video: any;
  isOpen: boolean;
  onClose: () => void;
}

const VideoModal: React.FC<VideoModalProps> = ({ video, isOpen, onClose }) => {
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen || !video) return null;

  const embedUrl = youtubeService.getEmbedUrl(video.id);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75" onClick={onClose}>
      <div className="relative w-full max-w-4xl mx-4" onClick={(e) => e.stopPropagation()}>
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute -top-10 right-0 text-white hover:text-gray-300 transition-colors z-10"
          aria-label="Close video"
        >
          <X size={32} />
        </button>

        {/* Video container */}
        <div className="bg-white rounded-lg overflow-hidden shadow-2xl">
          {/* Video iframe */}
          <div className="relative aspect-video">
            <iframe
              src={embedUrl}
              title={video.title}
              className="w-full h-full"
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            />
          </div>

          {/* Video info */}
          <div className="p-4 bg-white">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">{video.title}</h3>
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>{video.channelTitle}</span>
              <div className="flex items-center gap-4">
                <span className="flex items-center">
                  <Clock size={14} className="mr-1" />
                  {video.duration}
                </span>
                <span className="flex items-center">
                  <Eye size={14} className="mr-1" />
                  {video.viewCount}
                </span>
                <a
                  href={video.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center text-blue-600 hover:text-blue-800 transition-colors"
                >
                  <span>Watch on YouTube</span>
                  <ExternalLink size={14} className="ml-1" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const ImageCard: React.FC<ImageCardProps> = ({ image, index }) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleImageError = () => {
    setImageError(true);
    setIsLoading(false);
  };

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-200 group">
      <div className="relative aspect-video bg-gray-100">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
          </div>
        )}

        {imageError ? (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-50">
            <ImageIcon size={24} className="text-gray-400 mb-2" />
            <span className="text-xs text-gray-500">Image unavailable</span>
          </div>
        ) : (
          <img
            src={image.thumbnailUrl}
            alt={image.title}
            className={`w-full h-full object-cover transition-all duration-200 group-hover:scale-105 ${
              isLoading ? 'opacity-0' : 'opacity-100'
            }`}
            onError={handleImageError}
            onLoad={handleImageLoad}
            loading="lazy"
          />
        )}

        {/* Overlay with actions */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 flex items-center justify-center">
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-2">
            <a
              href={image.url}
              target="_blank"
              rel="noopener noreferrer"
              className="bg-white text-gray-800 p-2 rounded-full hover:bg-gray-100 transition-colors shadow-lg"
              title="View full image"
            >
              <ExternalLink size={14} />
            </a>
          </div>
        </div>

        {/* Index badge */}
        <div className="absolute top-2 left-2">
          <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-medium shadow-lg">
            {index + 1}
          </span>
        </div>
      </div>

      <div className="p-3">
        <h4 className="text-sm font-medium text-gray-900 line-clamp-2 mb-1">
          {image.title}
        </h4>
        <p className="text-xs text-gray-500 mb-2 line-clamp-1">
          {image.source}
        </p>
        {image.description && (
          <p className="text-xs text-gray-600 line-clamp-2">
            {image.description}
          </p>
        )}
      </div>
    </div>
  );
};

const AIResponse: React.FC<AIResponseProps> = ({ response }) => {
  const [activeTab, setActiveTab] = useState<'explanation' | 'solution' | 'sources' | 'videos' | 'images'>('explanation');
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['main']));
  const [selectedVideo, setSelectedVideo] = useState<any>(null);
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const handleVideoClick = (video: any) => {
    setSelectedVideo(video);
    setIsVideoModalOpen(true);
  };

  const closeVideoModal = () => {
    setIsVideoModalOpen(false);
    setSelectedVideo(null);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'basic': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getGATERelevanceStars = (relevance: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={12}
        className={i < Math.floor(relevance / 2) ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ));
  };

  return (
    <div className="space-y-4">
      {/* Main Answer */}
      <div className="bg-blue-50 border-l-4 border-blue-500 p-4 rounded-r-lg">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="font-semibold text-blue-900 mb-2 flex items-center">
              <Target size={16} className="mr-2" />
              Quick Answer
            </h3>
            <p className="text-blue-800">{response.answer}</p>
          </div>
          <div className="flex items-center gap-2 ml-4">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(response.difficulty)}`}>
              {response.difficulty}
            </span>
            <div className="flex items-center gap-1">
              {getGATERelevanceStars(response.gateRelevance)}
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          {[
            { id: 'explanation', label: 'Explanation', icon: BookOpen, count: null },
            ...(response.stepByStepSolution ? [{ id: 'solution', label: 'Step-by-Step Solution', icon: FileText, count: response.stepByStepSolution.steps?.length || 0 }] : []),
            { id: 'sources', label: 'Web Sources', icon: Link, count: response.webSources.length },
            { id: 'videos', label: 'Videos', icon: Play, count: response.youtubeVideos.length },
            { id: 'images', label: 'Images', icon: ImageIcon, count: response.images.length }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon size={16} />
              {tab.label}
              {tab.count !== null && tab.count > 0 && (
                <span className="bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full text-xs">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-[200px]">
        {activeTab === 'explanation' && (
          <div className="space-y-4">
            {/* Detailed Explanation */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <button
                onClick={() => toggleSection('explanation')}
                className="flex items-center justify-between w-full text-left"
              >
                <h4 className="font-semibold text-gray-900 flex items-center">
                  <Lightbulb size={16} className="mr-2 text-yellow-500" />
                  Detailed Explanation
                </h4>
                {expandedSections.has('explanation') ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
              </button>
              {expandedSections.has('explanation') && (
                <div className="mt-3 text-gray-700 leading-relaxed">
                  <MarkdownRenderer content={response.explanation} />
                </div>
              )}
            </div>

            {/* Key Points */}
            {response.keyPoints.length > 0 && (
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <button
                  onClick={() => toggleSection('keypoints')}
                  className="flex items-center justify-between w-full text-left"
                >
                  <h4 className="font-semibold text-gray-900 flex items-center">
                    <Target size={16} className="mr-2 text-blue-500" />
                    Key Points
                  </h4>
                  {expandedSections.has('keypoints') ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                </button>
                {expandedSections.has('keypoints') && (
                  <ul className="mt-3 space-y-2">
                    {response.keyPoints.map((point, index) => (
                      <li key={index} className="flex items-start">
                        <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full mr-3 mt-0.5">
                          {index + 1}
                        </span>
                        <span className="text-gray-700">{point}</span>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            )}

            {/* Formulas */}
            {response.formulas && response.formulas.length > 0 && (
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <button
                  onClick={() => toggleSection('formulas')}
                  className="flex items-center justify-between w-full text-left"
                >
                  <h4 className="font-semibold text-gray-900 flex items-center">
                    <Calculator size={16} className="mr-2 text-green-500" />
                    Formulas & Equations
                  </h4>
                  {expandedSections.has('formulas') ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                </button>
                {expandedSections.has('formulas') && (
                  <div className="mt-3 space-y-4">
                    {response.formulas.map((formula, index) => (
                      <div key={index} className="bg-gray-50 p-3 rounded-lg">
                        <h5 className="font-medium text-gray-900 mb-2">{formula.name}</h5>
                        <div className="bg-white p-3 rounded border text-center mb-2">
                          <MathRenderer
                            formula={formula.formula}
                            displayMode={true}
                            className="text-lg"
                          />
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{formula.description}</p>
                        {formula.units && (
                          <p className="text-xs text-gray-500">Units: {formula.units}</p>
                        )}
                        {formula.variables && formula.variables.length > 0 && (
                          <div className="mt-2">
                            <p className="text-xs font-medium text-gray-700 mb-1">Where:</p>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-1 text-xs text-gray-600">
                              {formula.variables.map((variable, vIndex) => (
                                <div key={vIndex}>
                                  <span className="font-mono font-medium">{variable.symbol}</span> = {variable.description}
                                  {variable.unit && <span className="text-gray-500"> ({variable.unit})</span>}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Related Topics */}
            {response.relatedTopics.length > 0 && (
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-3">Related Topics</h4>
                <div className="flex flex-wrap gap-2">
                  {response.relatedTopics.map((topic, index) => (
                    <span
                      key={index}
                      className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm hover:bg-purple-200 cursor-pointer transition-colors"
                    >
                      {topic}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'solution' && response.stepByStepSolution && (
          <div className="space-y-6">
            {/* Problem Overview */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-semibold text-blue-900 mb-3 flex items-center">
                <Target size={16} className="mr-2" />
                Problem Overview
              </h3>

              {/* Problem Type */}
              <div className="mb-3">
                <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                  response.stepByStepSolution.problemType === 'numerical' ? 'bg-green-100 text-green-800' :
                  response.stepByStepSolution.problemType === 'derivation' ? 'bg-purple-100 text-purple-800' :
                  'bg-orange-100 text-orange-800'
                }`}>
                  {response.stepByStepSolution.problemType.charAt(0).toUpperCase() + response.stepByStepSolution.problemType.slice(1)} Problem
                </span>
              </div>

              {/* Solution Approach */}
              {response.stepByStepSolution.approach && (
                <div className="mb-4">
                  <h4 className="font-medium text-blue-800 mb-2">Solution Approach:</h4>
                  <div className="bg-white p-3 rounded border">
                    <p className="text-gray-700">{response.stepByStepSolution.approach}</p>
                  </div>
                </div>
              )}

              {/* Given Data */}
              {response.stepByStepSolution.givenData && response.stepByStepSolution.givenData.length > 0 && (
                <div className="mb-4">
                  <h4 className="font-medium text-blue-800 mb-2">Given Data:</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {response.stepByStepSolution.givenData.map((data, index) => (
                      <div key={index} className="bg-white p-3 rounded border">
                        <div className="font-medium text-gray-900">{data.parameter}</div>
                        <div className="text-blue-600 font-mono">
                          {data.value} {data.unit && <span className="text-gray-500">({data.unit})</span>}
                        </div>
                        {data.description && <div className="text-sm text-gray-600 mt-1">{data.description}</div>}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* To Find */}
              {response.stepByStepSolution.toFind && response.stepByStepSolution.toFind.length > 0 && (
                <div className="mb-4">
                  <h4 className="font-medium text-blue-800 mb-2">To Find:</h4>
                  <ul className="list-disc list-inside space-y-1">
                    {response.stepByStepSolution.toFind.map((item, index) => (
                      <li key={index} className="text-gray-700">{item}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Assumptions */}
              {response.stepByStepSolution.assumptions && response.stepByStepSolution.assumptions.length > 0 && (
                <div>
                  <h4 className="font-medium text-blue-800 mb-2">Assumptions:</h4>
                  <ul className="list-disc list-inside space-y-1">
                    {response.stepByStepSolution.assumptions.map((assumption, index) => (
                      <li key={index} className="text-gray-700">{assumption}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* Solution Steps */}
            <div className="space-y-4">
              <h3 className="font-semibold text-gray-900 flex items-center">
                <Calculator size={16} className="mr-2 text-green-500" />
                Solution Steps
              </h3>

              {response.stepByStepSolution.steps.map((step, index) => (
                <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-8 h-8 bg-green-100 text-green-800 rounded-full flex items-center justify-center font-bold text-sm mr-4">
                      {step.stepNumber}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-2">{step.title}</h4>
                      <p className="text-gray-700 mb-3">{step.description}</p>

                      {step.formula && (
                        <div className="mb-3">
                          <h5 className="text-sm font-medium text-gray-800 mb-1">Formula:</h5>
                          <div className="bg-gray-50 p-3 rounded border">
                            <MathRenderer formula={step.formula} displayMode={true} />
                          </div>
                        </div>
                      )}

                      {step.substitution && (
                        <div className="mb-3">
                          <h5 className="text-sm font-medium text-gray-800 mb-1">Substitution:</h5>
                          <div className="bg-blue-50 p-3 rounded border">
                            <MarkdownRenderer content={step.substitution} className="text-sm" />
                          </div>
                        </div>
                      )}

                      {step.calculation && (
                        <div className="mb-3">
                          <h5 className="text-sm font-medium text-gray-800 mb-1">Calculation:</h5>
                          <div className="bg-yellow-50 p-3 rounded border">
                            <MarkdownRenderer content={step.calculation} className="text-sm" />
                          </div>
                        </div>
                      )}

                      {step.result && (
                        <div className="mb-3">
                          <h5 className="text-sm font-medium text-gray-800 mb-1">Result:</h5>
                          <div className="bg-green-50 p-3 rounded border font-semibold text-green-800">
                            <MarkdownRenderer content={step.result} />
                          </div>
                        </div>
                      )}

                      {step.explanation && (
                        <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded border-l-4 border-blue-300">
                          <strong>Why this step:</strong> {step.explanation}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Final Answer */}
            {response.stepByStepSolution.finalAnswer && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h3 className="font-semibold text-green-900 mb-2 flex items-center">
                  <CheckCircle size={16} className="mr-2" />
                  Final Answer
                </h3>
                <div className="text-lg font-bold text-green-800 bg-white p-3 rounded border">
                  <MarkdownRenderer content={response.stepByStepSolution.finalAnswer} />
                </div>
              </div>
            )}

            {/* Verification */}
            {response.stepByStepSolution.verification && (
              <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                <h3 className="font-semibold text-purple-900 mb-2 flex items-center">
                  <CheckCircle size={16} className="mr-2" />
                  Verification Methods
                </h3>
                <div className="text-purple-800">
                  <MarkdownRenderer content={response.stepByStepSolution.verification} />
                </div>
              </div>
            )}

            {/* Alternative Method */}
            {response.stepByStepSolution.alternativeMethod && (
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                <h3 className="font-semibold text-orange-900 mb-2 flex items-center">
                  <FileText size={16} className="mr-2" />
                  Alternative Solution Method
                </h3>
                <div className="text-orange-800">
                  <MarkdownRenderer content={response.stepByStepSolution.alternativeMethod} />
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'sources' && (
          <div className="space-y-4">
            {response.webSources.length > 0 ? (
              <>
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-gray-900">Educational Web Sources</h3>
                  <span className="text-sm text-gray-500">{response.webSources.length} sources found</span>
                </div>
                <div className="space-y-3">
                  {response.webSources.map((source, index) => (
                    <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all duration-200 group">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium">
                              #{index + 1}
                            </span>
                            <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                              source.relevanceScore > 0.8 ? 'bg-green-100 text-green-800' :
                              source.relevanceScore > 0.6 ? 'bg-yellow-100 text-yellow-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {source.relevanceScore > 0.8 ? 'High Relevance' :
                               source.relevanceScore > 0.6 ? 'Medium Relevance' : 'Low Relevance'}
                            </span>
                          </div>

                          <h4 className="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
                            <a href={source.url} target="_blank" rel="noopener noreferrer">
                              {source.title}
                            </a>
                          </h4>

                          <p className="text-gray-600 text-sm mb-3 line-clamp-3">{source.snippet}</p>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <span className="text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded">
                                {source.domain}
                              </span>
                              <div className="flex items-center">
                                <Star size={12} className="text-yellow-500 mr-1" />
                                <span className="text-xs text-gray-600">{(source.relevanceScore * 10).toFixed(1)}/10</span>
                              </div>
                            </div>

                            <div className="flex items-center gap-2">
                              <a
                                href={source.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-1 text-blue-600 hover:text-blue-800 transition-colors text-sm font-medium"
                              >
                                <span>Visit</span>
                                <ExternalLink size={14} />
                              </a>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            ) : (
              <div className="text-center py-12 text-gray-500">
                <Link size={64} className="mx-auto mb-4 opacity-30" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Web Sources Found</h3>
                <p className="text-gray-600 mb-4">We couldn't find relevant web sources for this query.</p>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
                  <h4 className="font-medium text-blue-900 mb-2">Possible reasons:</h4>
                  <ul className="text-sm text-blue-800 space-y-1 text-left">
                    <li>• Web search API may not be configured</li>
                    <li>• Query may be too specific</li>
                    <li>• Network connectivity issues</li>
                    <li>• API quota may be exceeded</li>
                  </ul>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'videos' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {response.youtubeVideos.length > 0 ? (
              response.youtubeVideos.map((video, index) => (
                <div key={index} className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow cursor-pointer" onClick={() => handleVideoClick(video)}>
                  <div className="relative">
                    <img
                      src={video.thumbnailUrl}
                      alt={video.title}
                      className="w-full h-32 object-cover"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                      <button
                        className="bg-red-600 text-white p-3 rounded-full hover:bg-red-700 transition-colors"
                        aria-label={`Play video: ${video.title}`}
                      >
                        <Play size={20} />
                      </button>
                    </div>
                  </div>
                  <div className="p-3">
                    <h4 className="font-medium text-gray-900 text-sm mb-1 line-clamp-2">{video.title}</h4>
                    <p className="text-xs text-gray-600 mb-2">{video.channelTitle}</p>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span className="flex items-center">
                        <Clock size={12} className="mr-1" />
                        {video.duration}
                      </span>
                      <span className="flex items-center">
                        <Eye size={12} className="mr-1" />
                        {video.viewCount}
                      </span>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-span-2 text-center py-8 text-gray-500">
                <Play size={48} className="mx-auto mb-2 opacity-50" />
                <p>No videos found for this query</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'images' && (
          <div className="space-y-4">
            {response.images.length > 0 ? (
              <>
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-gray-900">Related Images & Diagrams</h3>
                  <span className="text-sm text-gray-500">{response.images.length} images found</span>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {response.images.map((image, index) => (
                    <div key={index} className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-200 group">
                      <div className="relative aspect-video bg-gray-100">
                        <img
                          src={image.thumbnailUrl}
                          alt={image.title}
                          className="w-full h-full object-cover transition-all duration-200 group-hover:scale-105"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = `data:image/svg+xml;base64,${btoa(`
                              <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
                                <rect width="100%" height="100%" fill="#E5E7EB"/>
                                <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="12" fill="#9CA3AF" text-anchor="middle" dy=".3em">Image Not Found</text>
                              </svg>
                            `)}`;
                          }}
                          loading="lazy"
                        />

                        {/* Overlay with actions */}
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 flex items-center justify-center">
                          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                            <a
                              href={image.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="bg-white text-gray-800 p-2 rounded-full hover:bg-gray-100 transition-colors shadow-lg"
                              title="View full image"
                            >
                              <ExternalLink size={14} />
                            </a>
                          </div>
                        </div>

                        {/* Index badge */}
                        <div className="absolute top-2 left-2">
                          <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-medium shadow-lg">
                            {index + 1}
                          </span>
                        </div>
                      </div>

                      <div className="p-3">
                        <h4 className="text-sm font-medium text-gray-900 line-clamp-2 mb-1">
                          {image.title}
                        </h4>
                        <p className="text-xs text-gray-500 mb-1">
                          {image.source}
                        </p>
                        {image.description && (
                          <p className="text-xs text-gray-600 line-clamp-2">
                            {image.description}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </>
            ) : (
              <div className="text-center py-12 text-gray-500">
                <ImageIcon size={64} className="mx-auto mb-4 opacity-30" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Images Found</h3>
                <p className="text-gray-600 mb-4">We couldn't find relevant images for this query.</p>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
                  <h4 className="font-medium text-blue-900 mb-2">Possible reasons:</h4>
                  <ul className="text-sm text-blue-800 space-y-1 text-left">
                    <li>• Image search API may not be configured</li>
                    <li>• Query may be too specific</li>
                    <li>• Network connectivity issues</li>
                    <li>• API quota may be exceeded</li>
                  </ul>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Video Modal */}
      <VideoModal
        video={selectedVideo}
        isOpen={isVideoModalOpen}
        onClose={closeVideoModal}
      />
    </div>
  );
};

export default AIResponse;
