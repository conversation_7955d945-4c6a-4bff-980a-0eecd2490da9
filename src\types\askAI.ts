// Types for Ask AI feature
export interface AIQuery {
  id: string;
  question: string;
  timestamp: string;
  response?: AIResponse;
  isLoading?: boolean;
}

export interface AIResponse {
  id: string;
  answer: string;
  explanation: string;
  keyPoints: string[];
  formulas?: Formula[];
  stepByStepSolution?: StepByStepSolution;
  webSources: WebSource[];
  youtubeVideos: YouTubeVideo[];
  images: ImageResult[];
  relatedTopics: string[];
  difficulty: 'basic' | 'intermediate' | 'advanced';
  gateRelevance: number; // 1-10 scale
  timestamp: string;
}

export interface Formula {
  name: string;
  formula: string;
  description: string;
  units?: string;
  variables?: Variable[];
}

export interface Variable {
  symbol: string;
  description: string;
  unit?: string;
}

export interface StepByStepSolution {
  problemType: 'numerical' | 'derivation' | 'conceptual';
  givenData?: GivenData[];
  toFind?: string[];
  assumptions?: string[];
  approach?: string;
  steps: SolutionStep[];
  finalAnswer?: string;
  verification?: string;
  alternativeMethod?: string;
}

export interface GivenData {
  parameter: string;
  value: string;
  unit?: string;
  description?: string;
}

export interface SolutionStep {
  stepNumber: number;
  title: string;
  description: string;
  formula?: string;
  substitution?: string;
  calculation?: string;
  result?: string;
  explanation?: string;
  diagram?: string;
  intermediateResults?: string[];
  units?: string;
}

export interface WebSource {
  title: string;
  url: string;
  snippet: string;
  domain: string;
  relevanceScore: number;
}

export interface YouTubeVideo {
  id: string;
  title: string;
  channelTitle: string;
  duration: string;
  viewCount: string;
  thumbnailUrl: string;
  url: string;
  relevanceScore: number;
}

export interface ImageResult {
  title: string;
  url: string;
  thumbnailUrl: string;
  source: string;
  description?: string;
}

export interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: string;
  query?: AIQuery;
}

export interface AskAIState {
  messages: ChatMessage[];
  isLoading: boolean;
  currentQuery: string;
  searchHistory: AIQuery[];
}

// Search filters for different content types
export interface SearchFilters {
  includeWebSearch: boolean;
  includeYouTube: boolean;
  includeImages: boolean;
  difficulty: 'all' | 'basic' | 'intermediate' | 'advanced';
  contentType: 'all' | 'theory' | 'numerical' | 'conceptual';
}

// Response formatting options
export interface ResponseFormat {
  includeFormulas: boolean;
  includeExamples: boolean;
  includeStepByStep: boolean;
  includeVisuals: boolean;
  simplifiedLanguage: boolean;
}
