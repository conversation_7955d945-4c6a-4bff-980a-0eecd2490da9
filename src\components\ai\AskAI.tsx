import React, { useState, useRef, useEffect } from 'react';
import {
  Send,
  Bot,
  User,
  Loader2,
  Search,
  Youtube,
  Image as ImageIcon,
  BookOpen,
  Settings,
  Lightbulb,
  Calculator,
  HelpCircle,
  Mic,
  Download,
  Contrast
} from 'lucide-react';
import { askAIService } from '../../lib/askAIService';
import { backendService } from '../../lib/supabase';
import { useAuth } from '../../contexts/AuthContext';
import { ChatMessage, SearchFilters, AIResponse } from '../../types/askAI';
import AIResponseComponent from './AIResponse';

const CHAT_HISTORY_KEY = 'askai_chat_history';

const AskAI: React.FC = () => {
  const { user } = useAuth();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentQuery, setCurrentQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const [isLoadingConversation, setIsLoadingConversation] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({
    includeWebSearch: true,
    includeYouTube: true,
    includeImages: true,
    difficulty: 'all',
    contentType: 'all',
    numericalOnly: false,
    enhancedCalculations: false
  });
  const [showFilters, setShowFilters] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [highContrast, setHighContrast] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [connectionStatus, setConnectionStatus] = useState<'online' | 'offline' | 'checking'>('online');
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [loadingStage, setLoadingStage] = useState('');
  const [exactSearchMode, setExactSearchMode] = useState(true);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const debounceRef = useRef<NodeJS.Timeout | null>(null);
  const recognitionRef = useRef<any>(null);

  // Sample questions for quick start
  const sampleQuestions = [
    "What is the difference between stress and strain?",
    "Calculate the stress in a steel rod of diameter 20mm subjected to a load of 50kN",
    "Derive the equation for bending stress in beams",
    "A heat engine operates between 800K and 300K. Calculate its maximum efficiency",
    "Find the Reynolds number for water flowing at 2 m/s through a 50mm diameter pipe",
    "Explain the working principle of a heat engine"
  ];

  // Load or create conversation on mount
  useEffect(() => {
    if (user) {
      loadOrCreateConversation();
    } else {
      // Fallback to localStorage for non-authenticated users
      const saved = localStorage.getItem(CHAT_HISTORY_KEY);
      if (saved) {
        setMessages(JSON.parse(saved));
      }
    }
  }, [user]);

  // Persist to localStorage for non-authenticated users
  useEffect(() => {
    if (!user) {
      localStorage.setItem(CHAT_HISTORY_KEY, JSON.stringify(messages));
    }
  }, [messages, user]);

  useEffect(() => {
    scrollToBottom();
  }, [messages, isLoading]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Load or create conversation
  const loadOrCreateConversation = async () => {
    if (!user) return;

    try {
      setIsLoadingConversation(true);

      // Get existing conversations
      const conversations = await backendService.getAIConversations(user.id);

      if (conversations.length > 0) {
        // Load the most recent active conversation
        const activeConversation = conversations.find(c => c.is_active) || conversations[0];
        setCurrentConversationId(activeConversation.id);

        // Load messages for this conversation
        const dbMessages = await backendService.getAIMessages(activeConversation.id);

        // Convert database messages to ChatMessage format
        const chatMessages: ChatMessage[] = dbMessages.map(msg => ({
          id: msg.id,
          type: msg.message_type as 'user' | 'ai',
          content: msg.content,
          timestamp: msg.created_at,
          query: msg.message_type === 'ai' && msg.ai_response_data ? {
            id: `query_${msg.id}`,
            question: '', // We'll need to get this from the previous user message
            timestamp: msg.created_at,
            response: msg.ai_response_data
          } : undefined
        }));

        setMessages(chatMessages);
        console.log(`✅ Loaded conversation with ${chatMessages.length} messages`);
      } else {
        // Create new conversation
        const newConversation = await backendService.createAIConversation(user.id, {
          title: 'Ask AI Chat',
          conversation_type: 'general'
        });
        setCurrentConversationId(newConversation.id);
        setMessages([]);
        console.log('✅ Created new conversation');
      }
    } catch (error) {
      console.error('❌ Error loading conversation:', error);
      // Fallback to localStorage
      const saved = localStorage.getItem(CHAT_HISTORY_KEY);
      if (saved) {
        setMessages(JSON.parse(saved));
      }
    } finally {
      setIsLoadingConversation(false);
    }
  };

  // Save message to database
  const saveMessageToDatabase = async (message: ChatMessage, aiResponseData?: any) => {
    if (!user || !currentConversationId) return;

    try {
      await backendService.createAIMessage(user.id, currentConversationId, {
        message_type: message.type,
        content: message.content,
        ai_response_data: aiResponseData,
        query_filters: message.type === 'user' ? filters : undefined,
        has_formulas: aiResponseData?.formulas?.length > 0,
        has_step_by_step: aiResponseData?.stepByStepSolution?.length > 0,
        has_images: aiResponseData?.images?.length > 0,
        has_videos: aiResponseData?.youtubeVideos?.length > 0,
        difficulty_assessed: aiResponseData?.difficulty,
        gate_relevance: aiResponseData?.gateRelevance
      });

      console.log(`✅ Saved ${message.type} message to database`);
    } catch (error) {
      console.error('❌ Error saving message to database:', error);
    }
  };

  // Keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!isLoading && currentQuery.trim()) {
        handleSubmitInternal();
      }
    }
  };

  // Debounced submit
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (debounceRef.current) return;
    handleSubmitInternal();
    debounceRef.current = setTimeout(() => {
      debounceRef.current = null;
    }, 1000);
  };

  const handleSubmitInternal = async () => {
    if (!currentQuery.trim() || isLoading) return;
    setError(null);
    const userMessage: ChatMessage = {
      id: `user_${Date.now()}`,
      type: 'user',
      content: currentQuery,
      timestamp: new Date().toISOString()
    };
    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);
    setLoadingProgress(0);
    setLoadingStage('Analyzing your question...');
    setCurrentQuery('');
    inputRef.current?.focus();

    // Save user message to database
    await saveMessageToDatabase(userMessage);

    // Simulate progress updates
    const progressInterval = setInterval(() => {
      setLoadingProgress(prev => {
        if (prev < 90) return prev + 10;
        return prev;
      });
    }, 500);

    try {
      setConnectionStatus('checking');
      setLoadingStage('Generating AI response...');
      const response = await askAIService.processQuery(userMessage.content, filters, messages);

      clearInterval(progressInterval);
      setLoadingProgress(100);
      setLoadingStage('Complete!');

      const aiMessage: ChatMessage = {
        id: `ai_${Date.now()}`,
        type: 'ai',
        content: response.answer,
        timestamp: new Date().toISOString(),
        query: {
          id: `query_${Date.now()}`,
          question: userMessage.content,
          timestamp: new Date().toISOString(),
          response
        }
      };
      setMessages(prev => [...prev, aiMessage]);
      setError(null);
      setRetryCount(0);
      setConnectionStatus('online');

      // Save AI message to database
      await saveMessageToDatabase(aiMessage, response);
    } catch (err) {
      clearInterval(progressInterval);
      setLoadingProgress(0);
      setLoadingStage('');
      setConnectionStatus('offline');
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';

      let userFriendlyMessage = 'Sorry, I encountered an error while processing your question.';

      if (errorMessage.includes('Rate limit')) {
        userFriendlyMessage = 'You\'re asking questions too quickly. Please wait a moment before trying again.';
      } else if (errorMessage.includes('timeout')) {
        userFriendlyMessage = 'The request took too long to process. Please try again with a simpler question.';
      } else if (errorMessage.includes('API key')) {
        userFriendlyMessage = 'AI service is not properly configured. Please contact support.';
      } else if (!navigator.onLine) {
        userFriendlyMessage = 'You appear to be offline. Please check your internet connection.';
      }

      setError(userFriendlyMessage);
      setRetryCount(prev => prev + 1);

      const errorChatMessage: ChatMessage = {
        id: `error_${Date.now()}`,
        type: 'ai',
        content: userFriendlyMessage,
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, errorChatMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // Voice input
  const handleVoiceInput = () => {
    if (!('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
      alert('Speech recognition is not supported in this browser.');
      return;
    }
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    if (!recognitionRef.current) {
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = false;
      recognitionRef.current.interimResults = false;
      recognitionRef.current.lang = 'en-US';
      recognitionRef.current.onresult = (event: any) => {
        setCurrentQuery(event.results[0][0].transcript);
        setIsListening(false);
        inputRef.current?.focus();
      };
      recognitionRef.current.onerror = () => {
        setIsListening(false);
      };
      recognitionRef.current.onend = () => setIsListening(false);
    }
    setIsListening(true);
    recognitionRef.current.start();
  };

  // Export chat as text
  const handleExportChat = () => {
    const text = messages.map((m) => `${m.type === 'user' ? 'You' : 'AI'}: ${m.content}`).join('\n\n');
    const blob = new Blob([text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'askai_chat.txt';
    a.click();
    URL.revokeObjectURL(url);
  };

  // High contrast mode
  const toggleContrast = () => setHighContrast((v) => !v);

  const handleSampleQuestion = (question: string) => {
    setCurrentQuery(question);
    inputRef.current?.focus();
  };

  const clearChat = async () => {
    if (user && currentConversationId) {
      try {
        // Create a new conversation
        const newConversation = await backendService.createAIConversation(user.id, {
          title: 'Ask AI Chat',
          conversation_type: 'general'
        });
        setCurrentConversationId(newConversation.id);
        setMessages([]);
        console.log('✅ Started new conversation');
      } catch (error) {
        console.error('❌ Error creating new conversation:', error);
        // Fallback to just clearing messages
        setMessages([]);
      }
    } else {
      // For non-authenticated users, just clear localStorage
      setMessages([]);
      localStorage.removeItem(CHAT_HISTORY_KEY);
    }
  };





  return (
    <div className={`flex flex-col h-full bg-gray-50 ${highContrast ? 'contrast-more' : ''}`} aria-label="Ask AI chat interface">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4 flex flex-wrap items-center justify-between gap-2">
        <div className="flex items-center gap-3">
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-2 rounded-lg">
            <Bot className="text-white" size={24} />
          </div>
          <div>
            <h1 className="text-xl font-bold text-gray-900">Ask AI</h1>
            <p className="text-sm text-gray-600">Your GATE ME study assistant</p>
          </div>
        </div>
        <div className="flex items-center gap-2 flex-wrap">
          {/* Connection Status Indicator */}
          <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs ${
            connectionStatus === 'online' ? 'bg-green-100 text-green-700' :
            connectionStatus === 'offline' ? 'bg-red-100 text-red-700' :
            'bg-yellow-100 text-yellow-700'
          }`}>
            <div className={`w-2 h-2 rounded-full ${
              connectionStatus === 'online' ? 'bg-green-500' :
              connectionStatus === 'offline' ? 'bg-red-500' :
              'bg-yellow-500 animate-pulse'
            }`}></div>
            {connectionStatus === 'online' ? 'Online' :
             connectionStatus === 'offline' ? 'Offline' : 'Connecting...'}
          </div>

          <button
            onClick={() => setFilters(prev => ({
              ...prev,
              numericalOnly: !prev.numericalOnly,
              enhancedCalculations: !prev.numericalOnly ? true : prev.enhancedCalculations
            }))}
            aria-label="Toggle numerical problems mode"
            className={`p-2 rounded-lg transition-colors ${
              filters.numericalOnly ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
            title={filters.numericalOnly ? 'Disable Numerical Mode' : 'Enable Numerical Problems Mode'}
          >
            <Calculator size={18} />
          </button>
          <button onClick={toggleContrast} aria-label="Toggle high contrast mode" className="p-2 rounded-lg bg-gray-100 text-gray-600 hover:bg-gray-200"><Contrast size={18} /></button>
          <button onClick={handleExportChat} aria-label="Export chat" className="p-2 rounded-lg bg-gray-100 text-gray-600 hover:bg-gray-200"><Download size={18} /></button>
          <button onClick={() => setShowFilters(!showFilters)} aria-label="Show filters" className={`p-2 rounded-lg transition-colors ${showFilters ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}><Settings size={18} /></button>

          {messages.length > 0 && <button onClick={clearChat} aria-label="Clear chat" className="px-3 py-1 text-sm bg-red-100 text-red-600 rounded-lg hover:bg-red-200 transition-colors">Clear Chat</button>}
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Search Filters</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-xs text-gray-600 mb-1 block">Content Sources</label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.includeWebSearch}
                    onChange={(e) => setFilters(prev => ({ ...prev, includeWebSearch: e.target.checked }))}
                    className="mr-2"
                  />
                  <Search size={14} className="mr-1" />
                  <span className="text-sm">Web Articles</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.includeYouTube}
                    onChange={(e) => setFilters(prev => ({ ...prev, includeYouTube: e.target.checked }))}
                    className="mr-2"
                  />
                  <Youtube size={14} className="mr-1" />
                  <span className="text-sm">YouTube Videos</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.includeImages}
                    onChange={(e) => setFilters(prev => ({ ...prev, includeImages: e.target.checked }))}
                    className="mr-2"
                  />
                  <ImageIcon size={14} className="mr-1" />
                  <span className="text-sm">Images</span>
                </label>
              </div>
            </div>

            <div>
              <label className="text-xs text-gray-600 mb-1 block">Difficulty Level</label>
              <select
                value={filters.difficulty}
                onChange={(e) => setFilters(prev => ({ ...prev, difficulty: e.target.value as any }))}
                className="w-full p-2 text-sm border border-gray-300 rounded-md"
              >
                <option value="all">All Levels</option>
                <option value="basic">Basic</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>

              <label className="text-xs text-gray-600 mb-1 block mt-2">Content Type</label>
              <select
                value={filters.contentType}
                onChange={(e) => setFilters(prev => ({ ...prev, contentType: e.target.value as any }))}
                className="w-full p-2 text-sm border border-gray-300 rounded-md"
              >
                <option value="all">All Types</option>
                <option value="theory">Theory</option>
                <option value="numerical">Numerical</option>
                <option value="conceptual">Conceptual</option>
              </select>

              <label className="text-xs text-gray-600 mb-1 block mt-2">Search Mode</label>
              <div className="space-y-1">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="searchMode"
                    checked={exactSearchMode}
                    onChange={() => setExactSearchMode(true)}
                    className="mr-2 text-blue-600"
                  />
                  <span className="text-sm">Exact Keywords</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="searchMode"
                    checked={!exactSearchMode}
                    onChange={() => setExactSearchMode(false)}
                    className="mr-2 text-blue-600"
                  />
                  <span className="text-sm">Broad Search</span>
                </label>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {exactSearchMode ? 'Only exact keyword matches' : 'Related content included'}
              </p>
            </div>
          </div>

          {/* Numerical Problems Section */}
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="text-sm font-semibold text-blue-900 mb-3 flex items-center">
              <Calculator size={16} className="mr-2" />
              Numerical Problems Mode
            </h4>
            <div className="space-y-3">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={filters.numericalOnly}
                  onChange={(e) => setFilters(prev => ({
                    ...prev,
                    numericalOnly: e.target.checked,
                    // Auto-enable enhanced calculations when numerical only is selected
                    enhancedCalculations: e.target.checked ? true : prev.enhancedCalculations
                  }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm font-medium text-blue-800">Numerical Problems Only</span>
              </label>
              <p className="text-xs text-blue-600 ml-6">
                Focus exclusively on numerical problems with detailed step-by-step calculations
              </p>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={filters.enhancedCalculations}
                  onChange={(e) => setFilters(prev => ({ ...prev, enhancedCalculations: e.target.checked }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm font-medium text-blue-800">Enhanced Calculations</span>
              </label>
              <p className="text-xs text-blue-600 ml-6">
                Show every mathematical step, substitutions, and multiple verification methods
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {isLoadingConversation ? (
          <div className="text-center py-12">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <Loader2 className="text-white animate-spin" size={32} />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading your conversation...</h2>
            <p className="text-gray-600">Please wait while we restore your chat history</p>
          </div>
        ) : messages.length === 0 ? (
          <div className="text-center py-12">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <HelpCircle className="text-white" size={32} />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Ask me anything about GATE ME!</h2>
            <p className="text-gray-600 mb-6">I can help with concepts, numerical problems, and study doubts</p>

            <div className="max-w-2xl mx-auto">
              <h3 className="text-sm font-medium text-gray-900 mb-3">Try these sample questions:</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {sampleQuestions.map((question, index) => (
                  <button
                    key={index}
                    onClick={() => handleSampleQuestion(question)}
                    className="p-3 text-left bg-white border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors text-sm"
                  >
                    <Lightbulb size={14} className="inline mr-2 text-yellow-500" />
                    {question}
                  </button>
                ))}
              </div>
            </div>
          </div>
        ) : (
          messages.map((message) => (
            <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className={`flex max-w-4xl ${message.type === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                <div className={`flex-shrink-0 ${message.type === 'user' ? 'ml-3' : 'mr-3'}`}>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    message.type === 'user'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gradient-to-r from-blue-500 to-purple-600 text-white'
                  }`}>
                    {message.type === 'user' ? <User size={16} /> : <Bot size={16} />}
                  </div>
                </div>

                <div className={`flex-1 ${message.type === 'user' ? 'text-right' : 'text-left'}`}>
                  {message.type === 'user' ? (
                    <div className="bg-blue-500 text-white p-3 rounded-lg inline-block max-w-md">
                      {message.content}
                    </div>
                  ) : (
                    <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                      {message.query?.response ? (
                        <AIResponseComponent response={message.query.response} />
                      ) : (
                        <p className="text-gray-900">{message.content}</p>
                      )}
                    </div>
                  )}
                  <div className="text-xs text-gray-500 mt-1">
                    {new Date(message.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}

        {isLoading && (
          <div className="flex justify-start">
            <div className="flex mr-3">
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 text-white flex items-center justify-center">
                <Bot size={16} />
              </div>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm min-w-[300px]">
              <div className="flex items-center gap-2 mb-3">
                <Loader2 className="animate-spin text-blue-500" size={16} />
                <span className="text-gray-600">{loadingStage || 'Thinking...'}</span>
              </div>
              {loadingProgress > 0 && (
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out"
                    style={{ width: `${loadingProgress}%` }}
                  ></div>
                </div>
              )}
            </div>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mx-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center">
                  <span className="text-red-600 text-sm font-medium">!</span>
                </div>
              </div>
              <div className="ml-3 flex-1">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
                <div className="mt-3 flex gap-2">
                  <button
                    onClick={() => {
                      setError(null);
                      if (messages.length > 0) {
                        const lastUserMessage = [...messages].reverse().find(m => m.type === 'user');
                        if (lastUserMessage) {
                          setCurrentQuery(lastUserMessage.content);
                        }
                      }
                    }}
                    className="text-sm bg-red-100 text-red-700 px-3 py-1 rounded hover:bg-red-200 transition-colors"
                  >
                    Try Again
                  </button>
                  <button
                    onClick={() => setError(null)}
                    className="text-sm text-red-600 hover:text-red-800 transition-colors"
                  >
                    Dismiss
                  </button>
                  {retryCount > 2 && (
                    <span className="text-xs text-red-500 self-center ml-2">
                      Multiple failures detected. Please check your connection.
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="bg-white border-t border-gray-200 p-4">
        {/* Numerical Mode Indicator */}
        {filters.numericalOnly && (
          <div className="mb-3">
            <div className="flex items-center justify-center mb-3">
              <div className="bg-blue-100 border border-blue-300 rounded-lg px-4 py-2 flex items-center space-x-2">
                <Calculator size={16} className="text-blue-600" />
                <span className="text-sm font-medium text-blue-800">
                  Numerical Problems Mode Active
                </span>
                {filters.enhancedCalculations && (
                  <span className="bg-blue-200 text-blue-800 text-xs px-2 py-1 rounded-full">
                    Enhanced
                  </span>
                )}
              </div>
            </div>

            {/* Example Problems */}
            {messages.length === 0 && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
                <h4 className="text-sm font-semibold text-blue-900 mb-2">Try these numerical problems:</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {[
                    "Calculate stress in a steel rod with force 1000N and area 50mm²",
                    "Find deflection of a cantilever beam with load 500N and length 2m",
                    "Determine heat transfer rate through a wall with ΔT=30°C",
                    "Calculate power required for a pump with flow rate 0.05 m³/s"
                  ].map((example, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentQuery(example)}
                      className="text-left text-xs text-blue-700 hover:text-blue-900 hover:bg-blue-100 p-2 rounded border border-blue-200 transition-colors"
                    >
                      {example}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        <form onSubmit={handleSubmit} className="flex gap-2" aria-label="Ask AI input form">
          <button type="button" onClick={handleVoiceInput} aria-label="Voice input" className={`p-3 rounded-lg ${isListening ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}> <Mic size={20} /> </button>
          <input
            ref={inputRef}
            type="text"
            value={currentQuery}
            onChange={(e) => setCurrentQuery(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={filters.numericalOnly ? "Enter your numerical problem (e.g., Calculate stress in a steel rod with force 1000N and area 50mm²)..." : "Ask your GATE ME doubt or numerical problem..."}
            className="flex-1 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={isLoading}
            aria-label="Ask AI input"
            autoFocus
          />
          <button
            type="submit"
            disabled={!currentQuery.trim() || isLoading}
            className="px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            aria-label="Send message"
          >
            {isLoading ? <Loader2 className="animate-spin" size={20} /> : <Send size={20} />}
          </button>
        </form>
      </div>
    </div>
  );
};

export default AskAI;
