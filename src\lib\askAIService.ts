import { geminiService } from './geminiService';
import { webSearchService } from './webSearchService';
import { youtubeService } from './youtubeService';
import { AIResponse, WebSource, YouTubeVideo, ImageResult, SearchFilters, ChatMessage } from '../types/askAI';

class AskAIService {
  private geminiApiKey: string;
  private googleSearchApiKey: string;
  private imageSearchUrl = 'https://www.googleapis.com/customsearch/v1';
  private imageSearchEngineId: string;
  private responseCache = new Map<string, { response: AIResponse; timestamp: number }>();
  private readonly CACHE_DURATION = 30 * 60 * 1000; // 30 minutes
  private readonly MAX_CACHE_SIZE = 100;
  private rateLimitMap = new Map<string, number[]>();
  private readonly RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
  private readonly MAX_REQUESTS_PER_MINUTE = 10;

  constructor() {
    this.geminiApiKey = import.meta.env.VITE_GEMINI_API_KEY || '';
    this.googleSearchApiKey = import.meta.env.VITE_GOOGLE_SEARCH_API_KEY || import.meta.env.VITE_GEMINI_API_KEY || '';
    this.imageSearchEngineId = import.meta.env.VITE_GOOGLE_SEARCH_ENGINE_ID || '036535e2252a74c96';

    if (!this.geminiApiKey) {
      console.warn('Gemini API key not configured. AI features will be limited.');
    }
    if (!this.googleSearchApiKey) {
      console.warn('Google Search API key not configured. Image and web search features will be limited.');
    }
  }

  async processQuery(question: string, filters: SearchFilters = this.getDefaultFilters(), conversationHistory: ChatMessage[] = []): Promise<AIResponse> {
    try {
      // Check rate limiting
      if (!this.checkRateLimit()) {
        throw new Error('Rate limit exceeded. Please wait before making another request.');
      }

      // Check cache first
      const cacheKey = this.generateCacheKey(question, filters);
      const cachedResponse = this.getCachedResponse(cacheKey);
      if (cachedResponse) {
        return cachedResponse;
      }

      // Validate API configuration
      if (!this.geminiApiKey) {
        return this.getFallbackResponse(question, 'Gemini API key not configured');
      }

      // Generate unique ID for this response
      const responseId = `ai_response_${Date.now()}`;

      // Parallel execution of different search types with timeout
      const [aiExplanation, webSources, youtubeVideos, images] = await Promise.all([
        this.withTimeout(this.generateAIExplanation(question, filters, conversationHistory), 15000),
        filters.includeWebSearch ? this.withTimeout(this.searchWebSources(question, filters), 10000) : Promise.resolve([]),
        filters.includeYouTube ? this.withTimeout(this.searchYouTubeVideos(question), 10000) : Promise.resolve([]),
        filters.includeImages ? this.withTimeout(this.searchImages(question), 8000) : Promise.resolve([])
      ]);

      // Parse AI explanation to extract structured data
      const structuredResponse = this.parseAIResponse(aiExplanation);

      const response: AIResponse = {
        id: responseId,
        answer: structuredResponse.answer,
        explanation: structuredResponse.explanation,
        keyPoints: structuredResponse.keyPoints,
        formulas: structuredResponse.formulas,
        stepByStepSolution: structuredResponse.stepByStepSolution,
        webSources: webSources.slice(0, 6), // Limit to top 6 sources
        youtubeVideos: youtubeVideos.slice(0, 4), // Limit to top 4 videos
        images: images.slice(0, 8), // Limit to top 8 images
        relatedTopics: structuredResponse.relatedTopics,
        difficulty: this.assessDifficulty(question),
        gateRelevance: this.assessGATERelevance(question),
        timestamp: new Date().toISOString()
      };

      // Cache the response
      this.cacheResponse(cacheKey, response);

      return response;
    } catch (error) {
      console.error('Error processing AI query:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return this.getFallbackResponse(question, errorMessage);
    }
  }

  private async generateAIExplanation(question: string, filters: SearchFilters, conversationHistory: ChatMessage[] = []): Promise<string> {
    const prompt = this.buildAIPrompt(question, filters, conversationHistory);
    return await geminiService.makeRequest(prompt);
  }

  private buildAIPrompt(question: string, filters: SearchFilters, conversationHistory: ChatMessage[] = []): string {
    // Build conversation context
    let contextString = '';
    if (conversationHistory.length > 0) {
      const recentHistory = conversationHistory.slice(-6); // Last 3 exchanges
      contextString = '\n\nConversation Context:\n';
      recentHistory.forEach((msg) => {
        if (msg.type === 'user') {
          contextString += `Student: ${msg.content}\n`;
        } else if (msg.type === 'ai' && msg.query?.response?.answer) {
          contextString += `Tutor: ${msg.query.response.answer}\n`;
        }
      });
      contextString += '\nCurrent Question:\n';
    }

    return `You are an expert GATE Mechanical Engineering tutor specializing in doubt resolution and concept explanation.
${contextString}
Student Question: "${question}"

Please provide a comprehensive, natural language response that includes:

1. **Direct Answer**: Start with a clear, direct answer to the question in 2-3 sentences.

2. **Detailed Explanation**: Provide a detailed explanation of the concept (200-300 words) using simple, student-friendly language.

3. **Key Points**: List 3-4 important key points about the topic.

4. **Formulas** (if applicable): Include relevant formulas with:
   - Formula name and mathematical expression
   - Description of what the formula represents
   - Units (SI units if applicable)
   - Variable definitions

5. **Step-by-Step Solution** (for numerical/derivation problems): Include:
   - Problem type (numerical/derivation/conceptual)
   - Given data with values and units
   - What needs to be found
   - Assumptions made
   - Detailed solution steps with calculations
   - Final answer with units
   - Verification method

6. **Related Topics**: Mention 2-3 related GATE ME topics.

Guidelines:
- Use simple, student-friendly language
- Focus on GATE ME syllabus relevance
- Include practical applications and examples
- For numerical problems, ALWAYS provide detailed step-by-step solutions
- For derivations, show each mathematical step clearly
- For conceptual questions, focus on explanation and key points
- Explain concepts from first principles
- Connect to other related GATE topics
- Use proper mathematical notation (LaTeX format for formulas)
- Include units and dimensions where applicable

IMPORTANT:
- If the question contains numbers, values, or asks to "calculate", "find", "determine", "solve" - it's a numerical problem
- If the question asks to "derive", "prove", "show that" - it's a derivation problem
- If the question asks "what is", "explain", "describe" - it's a conceptual problem
- Always include step-by-step solutions for numerical and derivation problems
- Use LaTeX notation for all mathematical expressions (e.g., \\frac{1}{2}, \\sqrt{x}, \\int, \\sum)

${filters.difficulty !== 'all' ? `Difficulty Level: ${filters.difficulty}` : ''}
${filters.contentType !== 'all' ? `Content Type: ${filters.contentType}` : ''}`;
  }

  private async searchWebSources(question: string, filters?: SearchFilters): Promise<WebSource[]> {
    try {
      // Determine search type based on question
      const searchType = this.determineSearchType(question);
      const difficulty = filters?.difficulty !== 'all' ? filters?.difficulty as 'basic' | 'intermediate' | 'advanced' : undefined;

      const searchResult = await webSearchService.searchArticles(question, {
        num: 8,
        searchType,
        difficulty
      });

      console.log('🔍 Web Search Results:', searchResult);

      if (!searchResult.items || searchResult.items.length === 0) {
        console.warn('No web search results found');
        return this.getFallbackWebSources(question);
      }

      // Sort by relevance and educational value
      const sortedResults = webSearchService.sortByRelevance(searchResult.items);

      return sortedResults.map((item, index) => ({
        title: item.title,
        url: item.link,
        snippet: webSearchService.extractCleanSnippet(item.snippet),
        domain: item.displayLink,
        relevanceScore: (item.pagemap as any)?.customMetadata?.relevanceScore || Math.max(0.9 - (index * 0.1), 0.1)
      }));
    } catch (error) {
      console.error('Web search failed:', error);
      return this.getFallbackWebSources(question);
    }
  }

  // Determine search type based on question content
  private determineSearchType(question: string): 'general' | 'tutorial' | 'notes' | 'problems' | 'reference' {
    const lowerQuestion = question.toLowerCase();

    if (lowerQuestion.includes('solve') || lowerQuestion.includes('calculate') || lowerQuestion.includes('find')) {
      return 'problems';
    } else if (lowerQuestion.includes('how to') || lowerQuestion.includes('step') || lowerQuestion.includes('procedure')) {
      return 'tutorial';
    } else if (lowerQuestion.includes('notes') || lowerQuestion.includes('study material')) {
      return 'notes';
    } else if (lowerQuestion.includes('book') || lowerQuestion.includes('reference') || lowerQuestion.includes('textbook')) {
      return 'reference';
    }

    return 'general';
  }

  // Fallback web sources when API fails
  private getFallbackWebSources(question: string): WebSource[] {
    return [
      {
        title: `${question} - GATE ME Study Material`,
        url: `https://www.google.com/search?q=${encodeURIComponent(question + ' GATE ME')}`,
        snippet: 'Comprehensive study material and notes for GATE Mechanical Engineering preparation.',
        domain: 'google.com',
        relevanceScore: 0.8
      },
      {
        title: `${question} - Tutorial and Examples`,
        url: `https://www.google.com/search?q=${encodeURIComponent(question + ' tutorial GATE')}`,
        snippet: 'Step-by-step tutorials and solved examples for better understanding.',
        domain: 'google.com',
        relevanceScore: 0.7
      },
      {
        title: `${question} - NPTEL Lectures`,
        url: `https://www.google.com/search?q=${encodeURIComponent(question + ' NPTEL mechanical engineering')}`,
        snippet: 'Video lectures and course materials from NPTEL for mechanical engineering topics.',
        domain: 'nptel.ac.in',
        relevanceScore: 0.9
      }
    ];
  }

  private async searchYouTubeVideos(question: string): Promise<YouTubeVideo[]> {
    try {
      const enhancedQuery = `${question} GATE ME mechanical engineering tutorial`;
      const searchResult = await youtubeService.searchVideos(enhancedQuery, 6, {
        duration: 'medium',
        order: 'relevance'
      });

      return searchResult.videos.map((video, index) => ({
        id: video.id,
        title: video.title,
        channelTitle: video.channelTitle,
        duration: video.duration || 'N/A',
        viewCount: video.viewCount || 'N/A',
        thumbnailUrl: video.thumbnail,
        url: `https://www.youtube.com/watch?v=${video.id}`,
        relevanceScore: Math.max(0.9 - (index * 0.15), 0.1)
      }));
    } catch (error) {
      console.error('YouTube search failed:', error);
      return [];
    }
  }

  private async searchImages(question: string): Promise<ImageResult[]> {
    try {
      // Check if API key is available
      if (!this.googleSearchApiKey) {
        console.warn('No Google Search API key available for image search');
        return this.getFallbackImages(question);
      }

      const params = new URLSearchParams({
        key: this.googleSearchApiKey,
        cx: this.imageSearchEngineId,
        q: `${question} GATE mechanical engineering diagram illustration`,
        searchType: 'image',
        num: '8',
        safe: 'active',
        imgType: 'photo',
        imgSize: 'medium',
        fileType: 'jpg,png,gif,svg'
      });

      const searchUrl = `${this.imageSearchUrl}?${params.toString()}`;
      console.log('🔍 Image Search Debug:');
      console.log('- URL:', searchUrl);
      console.log('- Search Engine ID:', this.imageSearchEngineId);
      console.log('- API Key available:', !!this.googleSearchApiKey);
      console.log('- API Key (first 10 chars):', this.googleSearchApiKey ? this.googleSearchApiKey.substring(0, 10) + '...' : 'Not set');

      const response = await fetch(searchUrl);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Image search API error:', response.status, errorText);

        if (response.status === 403) {
          console.error('API key may be invalid or quota exceeded');
        } else if (response.status === 400) {
          console.error('Invalid search parameters');
        }

        return this.getFallbackImages(question);
      }

      const data = await response.json();
      console.log('Image search response:', data);

      if (!data.items || data.items.length === 0) {
        console.warn('No images found in API response');
        return this.getFallbackImages(question);
      }

      return data.items.map((item: any, index: number) => ({
        title: item.title || `Image ${index + 1}`,
        url: item.link,
        thumbnailUrl: item.image?.thumbnailLink || item.image?.contextLink || item.link,
        source: item.displayLink || 'Unknown source',
        description: item.snippet || item.title || 'No description available'
      }));
    } catch (error) {
      console.error('Image search failed:', error);
      return this.getFallbackImages(question);
    }
  }

  private getFallbackImages(question: string): ImageResult[] {
    // Return some educational placeholder images using data URLs
    const createDataUrl = (width: number, height: number, color: string, text: string) => {
      return `data:image/svg+xml;base64,${btoa(`
        <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
          <rect width="100%" height="100%" fill="${color}"/>
          <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="14" fill="white" text-anchor="middle" dy=".3em">${text}</text>
        </svg>
      `)}`;
    };

    return [
      {
        title: `${question} - Educational Diagram`,
        url: createDataUrl(400, 300, '#4F46E5', 'Educational Content'),
        thumbnailUrl: createDataUrl(200, 150, '#4F46E5', 'Diagram'),
        source: 'Educational Resources',
        description: 'Educational diagram and illustration for better understanding'
      },
      {
        title: `${question} - Technical Illustration`,
        url: createDataUrl(400, 300, '#059669', 'Technical Illustration'),
        thumbnailUrl: createDataUrl(200, 150, '#059669', 'Technical'),
        source: 'Engineering Resources',
        description: 'Technical illustration for mechanical engineering concepts'
      },
      {
        title: `${question} - GATE ME Reference`,
        url: createDataUrl(400, 300, '#DC2626', 'GATE ME Reference'),
        thumbnailUrl: createDataUrl(200, 150, '#DC2626', 'GATE ME'),
        source: 'GATE Preparation',
        description: 'Reference material for GATE Mechanical Engineering'
      }
    ];
  }

  private parseAIResponse(aiResponse: string): {
    answer: string;
    explanation: string;
    keyPoints: string[];
    formulas: any[];
    relatedTopics: string[];
    stepByStepSolution?: any;
  } {
    try {
      // First try to parse as JSON (for backward compatibility)
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          const parsed = JSON.parse(jsonMatch[0]);
          return {
            answer: parsed.answer || 'AI explanation not available',
            explanation: parsed.explanation || 'Detailed explanation not available',
            keyPoints: parsed.keyPoints || [],
            formulas: parsed.formulas || [],
            relatedTopics: parsed.relatedTopics || [],
            stepByStepSolution: parsed.stepByStepSolution || undefined
          };
        } catch (jsonError) {
          // If JSON parsing fails, fall through to natural language parsing
        }
      }

      // Parse natural language response
      return this.parseNaturalLanguageResponse(aiResponse);
    } catch (error) {
      console.error('Failed to parse AI response:', error);
      return this.getBasicResponse(aiResponse);
    }
  }

  private parseNaturalLanguageResponse(response: string): {
    answer: string;
    explanation: string;
    keyPoints: string[];
    formulas: any[];
    relatedTopics: string[];
    stepByStepSolution?: any;
  } {
    // Extract answer (usually the first paragraph or sentences)
    const answerMatch = response.match(/^(.*?)(?:\n\n|\n(?=\d+\.|\*\*|#))/s);
    const answer = answerMatch ? answerMatch[1].trim() : response.substring(0, 200) + '...';

    // Extract explanation (look for detailed explanation section)
    const explanationMatch = response.match(/(?:explanation|detailed explanation)[:\s]*\n?(.*?)(?=\n(?:\d+\.|key points|formulas|step|related))/is);
    const explanation = explanationMatch ? explanationMatch[1].trim() : response;

    // Extract key points
    const keyPoints = this.extractKeyPoints(response);

    // Extract formulas
    const formulas = this.extractFormulas(response);

    // Extract related topics
    const relatedTopics = this.extractRelatedTopics(response);

    // Extract step-by-step solution
    const stepByStepSolution = this.extractStepByStepSolution(response);

    return {
      answer,
      explanation,
      keyPoints,
      formulas,
      relatedTopics,
      stepByStepSolution
    };
  }

  private getBasicResponse(rawResponse: string): any {
    return {
      answer: rawResponse.substring(0, 200) + '...',
      explanation: rawResponse,
      keyPoints: [],
      formulas: [],
      relatedTopics: [],
      stepByStepSolution: undefined
    };
  }

  private extractKeyPoints(response: string): string[] {
    const keyPoints: string[] = [];

    // Look for numbered or bulleted key points
    const patterns = [
      /key points?[:\s]*\n?((?:[-*•]\s*.*?\n?)+)/is,
      /important points?[:\s]*\n?((?:[-*•]\s*.*?\n?)+)/is,
      /(?:^|\n)(?:\d+\.\s*)(.*?)(?=\n\d+\.|\n[A-Z]|\n$)/gm
    ];

    for (const pattern of patterns) {
      const match = response.match(pattern);
      if (match) {
        const pointsText = match[1] || match[0];
        const points = pointsText.split(/\n/).map(p => p.replace(/^[-*•\d.\s]+/, '').trim()).filter(p => p.length > 0);
        keyPoints.push(...points);
        break;
      }
    }

    // If no structured key points found, extract from content
    if (keyPoints.length === 0) {
      const sentences = response.split(/[.!?]+/).filter(s => s.trim().length > 20);
      keyPoints.push(...sentences.slice(0, 4).map(s => s.trim()));
    }

    return keyPoints.slice(0, 4);
  }

  private extractFormulas(response: string): any[] {
    const formulas: any[] = [];

    // Look for LaTeX formulas
    const latexMatches = response.match(/\\[a-zA-Z]+\{[^}]*\}|\\[a-zA-Z]+|[a-zA-Z]+\s*=\s*[^,\n]+/g);
    if (latexMatches) {
      latexMatches.forEach((formula, index) => {
        formulas.push({
          name: `Formula ${index + 1}`,
          formula: formula.trim(),
          description: `Mathematical expression`,
          units: '',
          variables: []
        });
      });
    }

    // Look for explicit formula sections
    const formulaMatch = response.match(/formulas?[:\s]*\n?(.*?)(?=\n(?:step|related|key))/is);
    if (formulaMatch && formulas.length === 0) {
      const formulaText = formulaMatch[1];
      const formulaLines = formulaText.split('\n').filter(line => line.trim().length > 0);
      formulaLines.forEach((line, index) => {
        if (line.includes('=') || line.includes('\\')) {
          formulas.push({
            name: `Formula ${index + 1}`,
            formula: line.trim(),
            description: `Mathematical expression`,
            units: '',
            variables: []
          });
        }
      });
    }

    return formulas;
  }

  private extractRelatedTopics(response: string): string[] {
    const relatedTopics: string[] = [];

    // Look for related topics section
    const patterns = [
      /related topics?[:\s]*\n?((?:[-*•]\s*.*?\n?)+)/is,
      /see also[:\s]*\n?((?:[-*•]\s*.*?\n?)+)/is,
      /other topics?[:\s]*\n?((?:[-*•]\s*.*?\n?)+)/is
    ];

    for (const pattern of patterns) {
      const match = response.match(pattern);
      if (match) {
        const topicsText = match[1];
        const topics = topicsText.split(/\n/).map(t => t.replace(/^[-*•\s]+/, '').trim()).filter(t => t.length > 0);
        relatedTopics.push(...topics);
        break;
      }
    }

    return relatedTopics.slice(0, 3);
  }

  private extractStepByStepSolution(response: string): any | undefined {
    // Look for step-by-step solution section
    const stepMatch = response.match(/step.by.step|solution|steps?[:\s]*\n?(.*?)(?=\n(?:related|verification|$))/is);
    if (!stepMatch) return undefined;

    const stepsText = stepMatch[1];
    const steps: any[] = [];

    // Extract individual steps
    const stepLines = stepsText.split(/\n/).filter(line => line.trim().length > 0);
    let stepNumber = 1;

    stepLines.forEach(line => {
      if (line.match(/step\s*\d+|^\d+\./i) || line.includes(':')) {
        steps.push({
          stepNumber: stepNumber++,
          title: `Step ${stepNumber - 1}`,
          description: line.replace(/^step\s*\d+[:\s]*/i, '').replace(/^\d+\.\s*/, '').trim(),
          formula: '',
          calculation: '',
          result: '',
          explanation: ''
        });
      }
    });

    if (steps.length === 0) return undefined;

    return {
      problemType: 'numerical',
      givenData: [],
      toFind: [],
      assumptions: [],
      steps,
      finalAnswer: '',
      verification: '',
      alternativeMethod: ''
    };
  }

  private assessDifficulty(question: string): 'basic' | 'intermediate' | 'advanced' {
    const basicKeywords = ['what is', 'define', 'basic', 'simple', 'introduction'];
    const advancedKeywords = ['derive', 'prove', 'complex', 'advanced', 'analysis', 'optimization'];

    const lowerQuestion = question.toLowerCase();

    if (basicKeywords.some(keyword => lowerQuestion.includes(keyword))) {
      return 'basic';
    } else if (advancedKeywords.some(keyword => lowerQuestion.includes(keyword))) {
      return 'advanced';
    }
    return 'intermediate';
  }

  private assessGATERelevance(question: string): number {
    const gateKeywords = [
      'gate', 'mechanical engineering', 'thermodynamics', 'fluid mechanics',
      'heat transfer', 'machine design', 'manufacturing', 'strength of materials',
      'engineering mechanics', 'theory of machines', 'vibrations'
    ];

    const lowerQuestion = question.toLowerCase();
    const relevantKeywords = gateKeywords.filter(keyword =>
      lowerQuestion.includes(keyword)
    );

    return Math.min(10, Math.max(1, relevantKeywords.length * 2));
  }

  private getDefaultFilters(): SearchFilters {
    return {
      includeWebSearch: true,
      includeYouTube: true,
      includeImages: true,
      difficulty: 'all',
      contentType: 'all'
    };
  }

  private getFallbackResponse(question: string, errorMessage?: string): AIResponse {
    return {
      id: `fallback_${Date.now()}`,
      answer: 'I apologize, but I encountered an error while processing your question.',
      explanation: errorMessage || 'Please try rephrasing your question or check your internet connection.',
      keyPoints: ['Try rephrasing your question', 'Check internet connection', 'Contact support if issue persists'],
      webSources: [],
      youtubeVideos: [],
      images: [],
      relatedTopics: [],
      difficulty: 'basic',
      gateRelevance: 1,
      timestamp: new Date().toISOString()
    };
  }

  // Cache management methods
  private generateCacheKey(question: string, filters: SearchFilters): string {
    return `${question.toLowerCase().trim()}_${JSON.stringify(filters)}`;
  }

  private getCachedResponse(cacheKey: string): AIResponse | null {
    const cached = this.responseCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.response;
    }
    if (cached) {
      this.responseCache.delete(cacheKey);
    }
    return null;
  }

  private cacheResponse(cacheKey: string, response: AIResponse): void {
    // Clean old cache entries if at max size
    if (this.responseCache.size >= this.MAX_CACHE_SIZE) {
      const oldestKey = this.responseCache.keys().next().value;
      if (oldestKey) {
        this.responseCache.delete(oldestKey);
      }
    }

    this.responseCache.set(cacheKey, {
      response,
      timestamp: Date.now()
    });
  }

  // Rate limiting methods
  private checkRateLimit(): boolean {
    const now = Date.now();
    const userKey = 'default'; // In a real app, use user ID
    const requests = this.rateLimitMap.get(userKey) || [];

    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < this.RATE_LIMIT_WINDOW);

    if (validRequests.length >= this.MAX_REQUESTS_PER_MINUTE) {
      return false;
    }

    validRequests.push(now);
    this.rateLimitMap.set(userKey, validRequests);
    return true;
  }

  // Timeout wrapper for API calls
  private withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    return Promise.race([
      promise,
      new Promise<T>((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), timeoutMs)
      )
    ]);
  }

  // Test function for image search configuration
  async testImageSearch(): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      if (!this.googleSearchApiKey) {
        return {
          success: false,
          message: 'Google Search API key not configured. Please add VITE_GOOGLE_SEARCH_API_KEY to your .env file.'
        };
      }

      if (!this.imageSearchEngineId) {
        return {
          success: false,
          message: 'Search Engine ID not configured. Please add VITE_GOOGLE_SEARCH_ENGINE_ID to your .env file.'
        };
      }

      // Test with a simple query
      const testImages = await this.searchImages('mechanical engineering');

      if (testImages.length > 0 && !testImages[0].url.includes('placeholder')) {
        return {
          success: true,
          message: `Image search working! Found ${testImages.length} images.`,
          details: { imageCount: testImages.length, firstImage: testImages[0] }
        };
      } else {
        return {
          success: false,
          message: 'Image search API configured but no real images returned. Check your Custom Search Engine setup.',
          details: { fallbackImages: testImages.length }
        };
      }
    } catch (error) {
      return {
        success: false,
        message: `Image search test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: { error }
      };
    }
  }
}

export const askAIService = new AskAIService();
