import { geminiService } from './geminiService';
import { webSearchService } from './webSearchService';
import { youtubeService } from './youtubeService';
import { AIResponse, WebSource, YouTubeVideo, ImageResult, SearchFilters, ChatMessage } from '../types/askAI';

class AskAIService {
  private geminiApiKey: string;
  private googleSearchApiKey: string;
  private imageSearchUrl = 'https://www.googleapis.com/customsearch/v1';
  private imageSearchEngineId: string;
  private responseCache = new Map<string, { response: AIResponse; timestamp: number }>();
  private readonly CACHE_DURATION = 30 * 60 * 1000; // 30 minutes
  private readonly MAX_CACHE_SIZE = 100;
  private rateLimitMap = new Map<string, number[]>();
  private readonly RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
  private readonly MAX_REQUESTS_PER_MINUTE = 10;

  constructor() {
    this.geminiApiKey = import.meta.env.VITE_GEMINI_API_KEY || '';
    this.googleSearchApiKey = import.meta.env.VITE_GOOGLE_SEARCH_API_KEY || import.meta.env.VITE_GEMINI_API_KEY || '';
    this.imageSearchEngineId = import.meta.env.VITE_GOOGLE_SEARCH_ENGINE_ID || '036535e2252a74c96';

    if (!this.geminiApiKey) {
      console.warn('Gemini API key not configured. AI features will be limited.');
    }
    if (!this.googleSearchApiKey) {
      console.warn('Google Search API key not configured. Image and web search features will be limited.');
    }
  }

  async processQuery(question: string, filters: SearchFilters = this.getDefaultFilters(), conversationHistory: ChatMessage[] = []): Promise<AIResponse> {
    try {
      // Check rate limiting
      if (!this.checkRateLimit()) {
        throw new Error('Rate limit exceeded. Please wait before making another request.');
      }

      // Check cache first
      const cacheKey = this.generateCacheKey(question, filters);
      const cachedResponse = this.getCachedResponse(cacheKey);
      if (cachedResponse) {
        return cachedResponse;
      }

      // Validate API configuration
      if (!this.geminiApiKey) {
        return this.getFallbackResponse(question, 'Gemini API key not configured');
      }

      // Generate unique ID for this response
      const responseId = `ai_response_${Date.now()}`;

      // Parallel execution of different search types with timeout
      const [aiExplanation, webSources, youtubeVideos, images] = await Promise.all([
        this.withTimeout(this.generateAIExplanation(question, filters, conversationHistory), 15000),
        filters.includeWebSearch ? this.withTimeout(this.searchWebSources(question, filters), 10000) : Promise.resolve([]),
        filters.includeYouTube ? this.withTimeout(this.searchYouTubeVideos(question), 10000) : Promise.resolve([]),
        filters.includeImages ? this.withTimeout(this.searchImages(question), 8000) : Promise.resolve([])
      ]);

      // Parse AI explanation to extract structured data
      const structuredResponse = this.parseAIResponse(aiExplanation);

      const response: AIResponse = {
        id: responseId,
        answer: structuredResponse.answer,
        explanation: structuredResponse.explanation,
        keyPoints: structuredResponse.keyPoints,
        formulas: structuredResponse.formulas,
        stepByStepSolution: structuredResponse.stepByStepSolution,
        webSources: webSources.slice(0, 6), // Limit to top 6 sources
        youtubeVideos: youtubeVideos.slice(0, 4), // Limit to top 4 videos
        images: images.slice(0, 8), // Limit to top 8 images
        relatedTopics: structuredResponse.relatedTopics,
        difficulty: this.assessDifficulty(question),
        gateRelevance: this.assessGATERelevance(question),
        timestamp: new Date().toISOString()
      };

      // Cache the response
      this.cacheResponse(cacheKey, response);

      return response;
    } catch (error) {
      console.error('Error processing AI query:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return this.getFallbackResponse(question, errorMessage);
    }
  }

  private async generateAIExplanation(question: string, filters: SearchFilters, conversationHistory: ChatMessage[] = []): Promise<string> {
    const prompt = this.buildAIPrompt(question, filters, conversationHistory);
    return await geminiService.makeRequest(prompt);
  }

  private buildAIPrompt(question: string, filters: SearchFilters, conversationHistory: ChatMessage[] = []): string {
    // Check if numerical problems only mode is enabled
    if (filters.numericalOnly) {
      return this.generateNumericalPrompt(question, filters, conversationHistory);
    }

    // Build conversation context
    let contextString = '';
    if (conversationHistory.length > 0) {
      const recentHistory = conversationHistory.slice(-6); // Last 3 exchanges
      contextString = '\n\nConversation Context:\n';
      recentHistory.forEach((msg) => {
        if (msg.type === 'user') {
          contextString += `Student: ${msg.content}\n`;
        } else if (msg.type === 'ai' && msg.query?.response?.answer) {
          contextString += `Tutor: ${msg.query.response.answer}\n`;
        }
      });
      contextString += '\nCurrent Question:\n';
    }

    return `You are an expert GATE Mechanical Engineering tutor specializing in doubt resolution and concept explanation.
${contextString}
Student Question: "${question}"

Please provide a comprehensive response with clear section headers. Structure your response as follows:

**DIRECT ANSWER:**
[Provide a clear, direct answer to the question in 2-3 sentences]

**DETAILED EXPLANATION:**
[Provide a detailed explanation of the concept (200-300 words) using simple, student-friendly language]

**KEY POINTS:**
- [Key point 1]
- [Key point 2]
- [Key point 3]
- [Key point 4]

**FORMULAS:** (if applicable)
For each formula, provide:
Formula Name: [Name]
Formula: $[LaTeX notation]$
Description: [What the formula represents]
Units: [SI units if applicable]
Variables: [Define each variable with units]

**STEP-BY-STEP SOLUTION:** (for numerical/derivation problems)
Problem Type: [numerical/derivation/conceptual]

Given Data:
- [Parameter 1]: [Value] [Unit] - [Description]
- [Parameter 2]: [Value] [Unit] - [Description]

To Find: [What needs to be determined]

Assumptions:
- [Assumption 1]
- [Assumption 2]

Solution Approach: [Brief overview of the solution method]

Solution Steps:
Step 1: [Descriptive Title]
Description: [Detailed description of what we're doing in this step]
Formula: $[Complete LaTeX formula with all variables defined]$
Substitution: [Show values being substituted: $variable = value$]
Calculation: [Show complete mathematical calculation step by step]
$[LaTeX calculation showing work]$
$= [intermediate result]$
$= [final result with units]$
Result: [Clear final result with proper units and significant figures]
Explanation: [Why this step is necessary and how it connects to the overall solution]

Step 2: [Descriptive Title]
Description: [Detailed description]
Formula: $[LaTeX formula]$
Substitution: [Values being substituted]
Calculation: [Complete calculation]
$[Step-by-step mathematical work]$
Result: [Result with units]
Explanation: [Reasoning and connection to next steps]

[Continue for all necessary steps...]

Final Answer: [Complete final answer with proper units, significant figures, and any relevant notes]

Verification Methods:
1. [Method 1]: [How to check the answer using alternative approach]
2. [Method 2]: [Dimensional analysis or order of magnitude check]
3. [Method 3]: [Physical reasonableness check]

Alternative Solution Method: [Brief description of alternative approach if applicable]

**RELATED TOPICS:**
- [Related GATE ME topic 1]
- [Related GATE ME topic 2]
- [Related GATE ME topic 3]

Guidelines:
- Use simple, student-friendly language
- Focus on GATE ME syllabus relevance
- Include practical applications and examples
- For numerical problems, ALWAYS provide detailed step-by-step solutions with calculations
- For derivations, show each mathematical step clearly
- Use LaTeX notation for ALL mathematical expressions: $\\frac{1}{2}$, $\\sqrt{x}$, $\\int$, $\\sum$, etc.
- Include units and dimensions where applicable
- Show actual numerical calculations, not just formulas

IMPORTANT FORMATTING RULES:
- If the question contains numbers, values, or asks to "calculate", "find", "determine", "solve" - it's a numerical problem
- If the question asks to "derive", "prove", "show that" - it's a derivation problem
- If the question asks "what is", "explain", "describe" - it's a conceptual problem
- Always include step-by-step solutions for numerical and derivation problems
- Use $ symbols to wrap LaTeX expressions for proper rendering
- For calculations, show EVERY mathematical step with actual numbers
- Include units in all calculations and results
- Use proper mathematical notation: $F = ma$, $P = \frac{F}{A}$, $\sigma = \frac{M \cdot y}{I}$
- Show substitution of values clearly: $F = 50 \times 9.81 = 490.5 \text{ N}$
- For complex calculations, break them into multiple lines with intermediate results
- Always verify answers using dimensional analysis or alternative methods

${filters.difficulty !== 'all' ? `Difficulty Level: ${filters.difficulty}` : ''}
${filters.contentType !== 'all' ? `Content Type: ${filters.contentType}` : ''}`;
  }

  private async searchWebSources(question: string, filters?: SearchFilters): Promise<WebSource[]> {
    try {
      // Determine search type based on question
      const searchType = this.determineSearchType(question);
      const difficulty = filters?.difficulty !== 'all' ? filters?.difficulty as 'basic' | 'intermediate' | 'advanced' : undefined;

      const searchResult = await webSearchService.searchArticles(question, {
        num: 8,
        searchType,
        difficulty
      });

      console.log('🔍 Web Search Results:', searchResult);

      if (!searchResult.items || searchResult.items.length === 0) {
        console.warn('No web search results found');
        return this.getFallbackWebSources(question);
      }

      // Sort by relevance and educational value
      const sortedResults = webSearchService.sortByRelevance(searchResult.items);

      return sortedResults.map((item, index) => ({
        title: item.title,
        url: item.link,
        snippet: webSearchService.extractCleanSnippet(item.snippet),
        domain: item.displayLink,
        relevanceScore: (item.pagemap as any)?.customMetadata?.relevanceScore || Math.max(0.9 - (index * 0.1), 0.1)
      }));
    } catch (error) {
      console.error('Web search failed:', error);
      return this.getFallbackWebSources(question);
    }
  }

  // Determine search type based on question content
  private determineSearchType(question: string): 'general' | 'tutorial' | 'notes' | 'problems' | 'reference' {
    const lowerQuestion = question.toLowerCase();

    if (lowerQuestion.includes('solve') || lowerQuestion.includes('calculate') || lowerQuestion.includes('find')) {
      return 'problems';
    } else if (lowerQuestion.includes('how to') || lowerQuestion.includes('step') || lowerQuestion.includes('procedure')) {
      return 'tutorial';
    } else if (lowerQuestion.includes('notes') || lowerQuestion.includes('study material')) {
      return 'notes';
    } else if (lowerQuestion.includes('book') || lowerQuestion.includes('reference') || lowerQuestion.includes('textbook')) {
      return 'reference';
    }

    return 'general';
  }

  // Fallback web sources when API fails
  private getFallbackWebSources(question: string): WebSource[] {
    return [
      {
        title: `${question} - GATE ME Study Material`,
        url: `https://www.google.com/search?q=${encodeURIComponent(question + ' GATE ME')}`,
        snippet: 'Comprehensive study material and notes for GATE Mechanical Engineering preparation.',
        domain: 'google.com',
        relevanceScore: 0.8
      },
      {
        title: `${question} - Tutorial and Examples`,
        url: `https://www.google.com/search?q=${encodeURIComponent(question + ' tutorial GATE')}`,
        snippet: 'Step-by-step tutorials and solved examples for better understanding.',
        domain: 'google.com',
        relevanceScore: 0.7
      },
      {
        title: `${question} - NPTEL Lectures`,
        url: `https://www.google.com/search?q=${encodeURIComponent(question + ' NPTEL mechanical engineering')}`,
        snippet: 'Video lectures and course materials from NPTEL for mechanical engineering topics.',
        domain: 'nptel.ac.in',
        relevanceScore: 0.9
      }
    ];
  }

  private async searchYouTubeVideos(question: string): Promise<YouTubeVideo[]> {
    try {
      const enhancedQuery = `${question} GATE ME mechanical engineering tutorial`;
      const searchResult = await youtubeService.searchVideos(enhancedQuery, 6, {
        duration: 'medium',
        order: 'relevance'
      });

      return searchResult.videos.map((video, index) => ({
        id: video.id,
        title: video.title,
        channelTitle: video.channelTitle,
        duration: video.duration || 'N/A',
        viewCount: video.viewCount || 'N/A',
        thumbnailUrl: video.thumbnail,
        url: `https://www.youtube.com/watch?v=${video.id}`,
        relevanceScore: Math.max(0.9 - (index * 0.15), 0.1)
      }));
    } catch (error) {
      console.error('YouTube search failed:', error);
      return [];
    }
  }

  private async searchImages(question: string): Promise<ImageResult[]> {
    try {
      // Check if API key is available
      if (!this.googleSearchApiKey) {
        console.warn('No Google Search API key available for image search');
        return this.getFallbackImages(question);
      }

      const params = new URLSearchParams({
        key: this.googleSearchApiKey,
        cx: this.imageSearchEngineId,
        q: `${question} GATE mechanical engineering diagram illustration`,
        searchType: 'image',
        num: '8',
        safe: 'active',
        imgType: 'photo',
        imgSize: 'medium',
        fileType: 'jpg,png,gif,svg'
      });

      const searchUrl = `${this.imageSearchUrl}?${params.toString()}`;
      console.log('🔍 Image Search Debug:');
      console.log('- URL:', searchUrl);
      console.log('- Search Engine ID:', this.imageSearchEngineId);
      console.log('- API Key available:', !!this.googleSearchApiKey);
      console.log('- API Key (first 10 chars):', this.googleSearchApiKey ? this.googleSearchApiKey.substring(0, 10) + '...' : 'Not set');

      const response = await fetch(searchUrl);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Image search API error:', response.status, errorText);

        if (response.status === 403) {
          console.error('API key may be invalid or quota exceeded');
        } else if (response.status === 400) {
          console.error('Invalid search parameters');
        }

        return this.getFallbackImages(question);
      }

      const data = await response.json();
      console.log('Image search response:', data);

      if (!data.items || data.items.length === 0) {
        console.warn('No images found in API response');
        return this.getFallbackImages(question);
      }

      return data.items.map((item: any, index: number) => ({
        title: item.title || `Image ${index + 1}`,
        url: item.link,
        thumbnailUrl: item.image?.thumbnailLink || item.image?.contextLink || item.link,
        source: item.displayLink || 'Unknown source',
        description: item.snippet || item.title || 'No description available'
      }));
    } catch (error) {
      console.error('Image search failed:', error);
      return this.getFallbackImages(question);
    }
  }

  private getFallbackImages(question: string): ImageResult[] {
    // Return some educational placeholder images using data URLs
    const createDataUrl = (width: number, height: number, color: string, text: string) => {
      return `data:image/svg+xml;base64,${btoa(`
        <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
          <rect width="100%" height="100%" fill="${color}"/>
          <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="14" fill="white" text-anchor="middle" dy=".3em">${text}</text>
        </svg>
      `)}`;
    };

    return [
      {
        title: `${question} - Educational Diagram`,
        url: createDataUrl(400, 300, '#4F46E5', 'Educational Content'),
        thumbnailUrl: createDataUrl(200, 150, '#4F46E5', 'Diagram'),
        source: 'Educational Resources',
        description: 'Educational diagram and illustration for better understanding'
      },
      {
        title: `${question} - Technical Illustration`,
        url: createDataUrl(400, 300, '#059669', 'Technical Illustration'),
        thumbnailUrl: createDataUrl(200, 150, '#059669', 'Technical'),
        source: 'Engineering Resources',
        description: 'Technical illustration for mechanical engineering concepts'
      },
      {
        title: `${question} - GATE ME Reference`,
        url: createDataUrl(400, 300, '#DC2626', 'GATE ME Reference'),
        thumbnailUrl: createDataUrl(200, 150, '#DC2626', 'GATE ME'),
        source: 'GATE Preparation',
        description: 'Reference material for GATE Mechanical Engineering'
      }
    ];
  }

  private parseAIResponse(aiResponse: string): {
    answer: string;
    explanation: string;
    keyPoints: string[];
    formulas: any[];
    relatedTopics: string[];
    stepByStepSolution?: any;
  } {
    try {
      // First try to parse as JSON (for backward compatibility)
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          const parsed = JSON.parse(jsonMatch[0]);
          return {
            answer: parsed.answer || 'AI explanation not available',
            explanation: parsed.explanation || 'Detailed explanation not available',
            keyPoints: parsed.keyPoints || [],
            formulas: parsed.formulas || [],
            relatedTopics: parsed.relatedTopics || [],
            stepByStepSolution: parsed.stepByStepSolution || undefined
          };
        } catch (jsonError) {
          // If JSON parsing fails, fall through to natural language parsing
        }
      }

      // Parse natural language response
      return this.parseNaturalLanguageResponse(aiResponse);
    } catch (error) {
      console.error('Failed to parse AI response:', error);
      return this.getBasicResponse(aiResponse);
    }
  }

  private parseNaturalLanguageResponse(response: string): {
    answer: string;
    explanation: string;
    keyPoints: string[];
    formulas: any[];
    relatedTopics: string[];
    stepByStepSolution?: any;
  } {
    // Extract direct answer from structured format
    const answerMatch = response.match(/\*\*DIRECT ANSWER:\*\*\s*(.*?)(?=\*\*[A-Z\s]+:\*\*|$)/is);
    const answer = answerMatch ? answerMatch[1].trim() : this.extractFallbackAnswer(response);

    // Extract detailed explanation from structured format
    const explanationMatch = response.match(/\*\*DETAILED EXPLANATION:\*\*\s*(.*?)(?=\*\*[A-Z\s]+:\*\*|$)/is);
    const explanation = explanationMatch ? explanationMatch[1].trim() : this.extractFallbackExplanation(response);

    // Extract key points
    const keyPoints = this.extractKeyPoints(response);

    // Extract formulas
    const formulas = this.extractFormulas(response);

    // Extract related topics
    const relatedTopics = this.extractRelatedTopics(response);

    // Extract step-by-step solution
    const stepByStepSolution = this.extractStepByStepSolution(response);

    return {
      answer,
      explanation,
      keyPoints,
      formulas,
      relatedTopics,
      stepByStepSolution
    };
  }

  private extractFallbackAnswer(response: string): string {
    // Try to extract the first meaningful paragraph
    const paragraphs = response.split('\n\n').filter(p => p.trim().length > 0);
    for (const paragraph of paragraphs) {
      if (paragraph.length > 50 && paragraph.length < 300 && !paragraph.includes('**')) {
        return paragraph.trim();
      }
    }
    return response.substring(0, 200) + '...';
  }

  private extractFallbackExplanation(response: string): string {
    // If no structured explanation found, use the whole response
    const cleanResponse = response.replace(/\*\*[^*]+\*\*/g, '').trim();
    return cleanResponse.length > 0 ? cleanResponse : response;
  }

  private getBasicResponse(rawResponse: string): any {
    return {
      answer: rawResponse.substring(0, 200) + '...',
      explanation: rawResponse,
      keyPoints: [],
      formulas: [],
      relatedTopics: [],
      stepByStepSolution: undefined
    };
  }

  private extractKeyPoints(response: string): string[] {
    const keyPoints: string[] = [];

    // Look for structured key points section
    const keyPointsMatch = response.match(/\*\*KEY POINTS:\*\*\s*(.*?)(?=\*\*[A-Z\s]+:\*\*|$)/is);
    if (keyPointsMatch) {
      const keyPointsText = keyPointsMatch[1];
      const lines = keyPointsText.split('\n').filter(line => line.trim().startsWith('-'));

      lines.forEach(line => {
        const point = line.replace(/^-\s*/, '').trim();
        if (point.length > 0) {
          keyPoints.push(point);
        }
      });
    }

    // Fallback: Look for numbered or bulleted key points
    if (keyPoints.length === 0) {
      const patterns = [
        /key points?[:\s]*\n?((?:[-*•]\s*.*?\n?)+)/is,
        /important points?[:\s]*\n?((?:[-*•]\s*.*?\n?)+)/is,
        /(?:^|\n)(?:\d+\.\s*)(.*?)(?=\n\d+\.|\n[A-Z]|\n$)/gm
      ];

      for (const pattern of patterns) {
        const match = response.match(pattern);
        if (match) {
          const pointsText = match[1] || match[0];
          const points = pointsText.split(/\n/).map(p => p.replace(/^[-*•\d.\s]+/, '').trim()).filter(p => p.length > 0);
          keyPoints.push(...points);
          break;
        }
      }
    }

    // If no structured key points found, extract from content
    if (keyPoints.length === 0) {
      const sentences = response.split(/[.!?]+/).filter(s => s.trim().length > 20);
      keyPoints.push(...sentences.slice(0, 4).map(s => s.trim()));
    }

    return keyPoints.slice(0, 4);
  }

  private extractFormulas(response: string): any[] {
    const formulas: any[] = [];

    // Look for formulas section with structured format
    const formulasSectionMatch = response.match(/\*\*FORMULAS:\*\*\s*(.*?)(?=\*\*[A-Z\s]+:\*\*|$)/is);
    if (formulasSectionMatch) {
      const formulasText = formulasSectionMatch[1];

      // Extract individual formulas with their details
      const formulaBlocks = formulasText.split(/(?=Formula Name:|Name:)/i);

      formulaBlocks.forEach((block, index) => {
        if (block.trim().length === 0) return;

        const nameMatch = block.match(/(?:Formula Name|Name):\s*([^\n]+)/i);
        const formulaMatch = block.match(/Formula:\s*\$([^$]+)\$/i) || block.match(/Formula:\s*([^\n]+)/i);
        const descriptionMatch = block.match(/Description:\s*([^\n]+)/i);
        const unitsMatch = block.match(/Units:\s*([^\n]+)/i);
        const variablesMatch = block.match(/Variables:\s*([^\n]+)/i);

        if (formulaMatch) {
          formulas.push({
            name: nameMatch ? nameMatch[1].trim() : `Formula ${index + 1}`,
            formula: formulaMatch[1].trim(),
            description: descriptionMatch ? descriptionMatch[1].trim() : 'Mathematical expression',
            units: unitsMatch ? unitsMatch[1].trim() : '',
            variables: variablesMatch ? this.parseVariables(variablesMatch[1]) : []
          });
        }
      });
    }

    // Fallback: Look for LaTeX expressions in $ symbols
    if (formulas.length === 0) {
      const latexMatches = response.match(/\$([^$]+)\$/g);
      if (latexMatches) {
        latexMatches.forEach((match, index) => {
          const formula = match.replace(/\$/g, '').trim();
          if (formula.length > 2) { // Avoid single characters
            formulas.push({
              name: `Formula ${index + 1}`,
              formula: formula,
              description: 'Mathematical expression',
              units: '',
              variables: []
            });
          }
        });
      }
    }

    // Additional fallback: Look for common mathematical expressions
    if (formulas.length === 0) {
      const mathExpressions = response.match(/[A-Za-z]+\s*=\s*[^,\n.]+/g);
      if (mathExpressions) {
        mathExpressions.forEach((expr, index) => {
          formulas.push({
            name: `Expression ${index + 1}`,
            formula: expr.trim(),
            description: 'Mathematical expression',
            units: '',
            variables: []
          });
        });
      }
    }

    return formulas;
  }

  private parseVariables(variablesText: string): any[] {
    const variables: any[] = [];
    // Simple parsing for variables - can be enhanced
    const parts = variablesText.split(',');
    parts.forEach(part => {
      const trimmed = part.trim();
      if (trimmed.length > 0) {
        variables.push({
          symbol: trimmed.split(' ')[0] || trimmed,
          description: trimmed,
          unit: ''
        });
      }
    });
    return variables;
  }

  private extractRelatedTopics(response: string): string[] {
    const relatedTopics: string[] = [];

    // Look for related topics section
    const patterns = [
      /related topics?[:\s]*\n?((?:[-*•]\s*.*?\n?)+)/is,
      /see also[:\s]*\n?((?:[-*•]\s*.*?\n?)+)/is,
      /other topics?[:\s]*\n?((?:[-*•]\s*.*?\n?)+)/is
    ];

    for (const pattern of patterns) {
      const match = response.match(pattern);
      if (match) {
        const topicsText = match[1];
        const topics = topicsText.split(/\n/).map(t => t.replace(/^[-*•\s]+/, '').trim()).filter(t => t.length > 0);
        relatedTopics.push(...topics);
        break;
      }
    }

    return relatedTopics.slice(0, 3);
  }

  private extractStepByStepSolution(response: string): any | undefined {
    // Look for step-by-step solution section
    const solutionMatch = response.match(/\*\*STEP-BY-STEP SOLUTION:\*\*\s*(.*?)(?=\*\*[A-Z\s]+:\*\*|$)/is);
    if (!solutionMatch) {
      // Fallback to simpler pattern
      const fallbackMatch = response.match(/(?:step.by.step|solution|steps?)[:\s]*\n?(.*?)(?=\n(?:related|verification|$))/is);
      if (!fallbackMatch) return undefined;
    }

    const solutionText = solutionMatch ? solutionMatch[1] : '';

    // Extract problem type
    const problemTypeMatch = solutionText.match(/Problem Type:\s*([^\n]+)/i);
    const problemType = problemTypeMatch ? problemTypeMatch[1].trim() : 'numerical';

    // Extract given data
    const givenData = this.extractGivenData(solutionText);

    // Extract what to find
    const toFindMatch = solutionText.match(/To Find:\s*([^\n]+)/i);
    const toFind = toFindMatch ? [toFindMatch[1].trim()] : [];

    // Extract assumptions
    const assumptions = this.extractAssumptions(solutionText);

    // Extract solution steps
    const steps = this.extractSolutionSteps(solutionText);

    // Extract solution approach
    const approachMatch = solutionText.match(/Solution Approach:\s*(.*?)(?=Solution Steps:|$)/is);
    const approach = approachMatch ? approachMatch[1].trim() : '';

    // Extract final answer
    const finalAnswerMatch = solutionText.match(/Final Answer:\s*(.*?)(?=Verification|Alternative|$)/is);
    const finalAnswer = finalAnswerMatch ? finalAnswerMatch[1].trim() : '';

    // Extract verification methods
    const verification = this.extractVerificationMethods(solutionText);

    // Extract alternative solution method
    const alternativeMatch = solutionText.match(/Alternative Solution Method:\s*(.*?)(?=$)/is);
    const alternativeMethod = alternativeMatch ? alternativeMatch[1].trim() : '';

    if (steps.length === 0 && !finalAnswer) return undefined;

    return {
      problemType,
      givenData,
      toFind,
      assumptions,
      approach,
      steps,
      finalAnswer,
      verification,
      alternativeMethod
    };
  }

  private extractGivenData(solutionText: string): any[] {
    const givenData: any[] = [];
    const givenMatch = solutionText.match(/Given Data:\s*(.*?)(?=To Find:|Assumptions:|Solution Steps:|$)/is);

    if (givenMatch) {
      const givenText = givenMatch[1];
      const lines = givenText.split('\n').filter(line => line.trim().startsWith('-'));

      lines.forEach(line => {
        const cleanLine = line.replace(/^-\s*/, '').trim();
        const parts = cleanLine.split(':');
        if (parts.length >= 2) {
          const parameter = parts[0].trim();
          const valueAndUnit = parts[1].trim();
          const unitMatch = valueAndUnit.match(/([^-]+)\s*-\s*(.+)/);

          givenData.push({
            parameter,
            value: unitMatch ? unitMatch[1].trim() : valueAndUnit,
            unit: '',
            description: unitMatch ? unitMatch[2].trim() : ''
          });
        }
      });
    }

    return givenData;
  }

  private extractAssumptions(solutionText: string): string[] {
    const assumptions: string[] = [];
    const assumptionsMatch = solutionText.match(/Assumptions:\s*(.*?)(?=Solution Steps:|$)/is);

    if (assumptionsMatch) {
      const assumptionsText = assumptionsMatch[1];
      const lines = assumptionsText.split('\n').filter(line => line.trim().startsWith('-'));

      lines.forEach(line => {
        const assumption = line.replace(/^-\s*/, '').trim();
        if (assumption.length > 0) {
          assumptions.push(assumption);
        }
      });
    }

    return assumptions;
  }

  private extractSolutionSteps(solutionText: string): any[] {
    const steps: any[] = [];
    const stepsMatch = solutionText.match(/Solution Steps:\s*(.*?)(?=Final Answer:|Verification:|$)/is);

    if (stepsMatch) {
      const stepsText = stepsMatch[1];
      const stepBlocks = stepsText.split(/(?=Step \d+:)/i);

      stepBlocks.forEach((block, index) => {
        if (block.trim().length === 0) return;

        const stepNumberMatch = block.match(/Step (\d+):/i);
        const titleMatch = block.match(/Step \d+:\s*([^\n]+)/i);
        const descriptionMatch = block.match(/Description:\s*(.*?)(?=Formula:|Substitution:|Calculation:|Result:|Explanation:|Step \d+:|$)/is);
        const formulaMatch = block.match(/Formula:\s*\$([^$]+)\$/i);
        const substitutionMatch = block.match(/Substitution:\s*(.*?)(?=Calculation:|Result:|Explanation:|Step \d+:|$)/is);
        const calculationMatch = block.match(/Calculation:\s*(.*?)(?=Result:|Explanation:|Step \d+:|$)/is);
        const resultMatch = block.match(/Result:\s*(.*?)(?=Explanation:|Step \d+:|$)/is);
        const explanationMatch = block.match(/Explanation:\s*(.*?)(?=Step \d+:|$)/is);

        if (stepNumberMatch || titleMatch) {
          // Extract detailed calculation with LaTeX expressions
          let detailedCalculation = '';
          if (calculationMatch) {
            const calcText = calculationMatch[1].trim();
            // Look for LaTeX expressions in the calculation
            const latexExpressions = calcText.match(/\$[^$]+\$/g);
            if (latexExpressions) {
              detailedCalculation = calcText;
            } else {
              detailedCalculation = calculationMatch[1].trim();
            }
          }

          // Combine substitution and calculation for better display
          let fullCalculation = '';
          if (substitutionMatch && substitutionMatch[1].trim()) {
            fullCalculation += `**Substitution:** ${substitutionMatch[1].trim()}\n\n`;
          }
          if (detailedCalculation) {
            fullCalculation += `**Calculation:**\n${detailedCalculation}`;
          }

          steps.push({
            stepNumber: stepNumberMatch ? parseInt(stepNumberMatch[1]) : index + 1,
            title: titleMatch ? titleMatch[1].trim() : `Step ${index + 1}`,
            description: descriptionMatch ? descriptionMatch[1].trim() : '',
            formula: formulaMatch ? formulaMatch[1].trim() : '',
            calculation: fullCalculation || (calculationMatch ? calculationMatch[1].trim() : ''),
            result: resultMatch ? resultMatch[1].trim() : '',
            explanation: explanationMatch ? explanationMatch[1].trim() : '',
            substitution: substitutionMatch ? substitutionMatch[1].trim() : ''
          });
        }
      });
    }

    return steps;
  }

  private extractVerificationMethods(solutionText: string): string {
    const verificationMatch = solutionText.match(/Verification Methods:\s*(.*?)(?=Alternative|$)/is);
    if (verificationMatch) {
      const verificationText = verificationMatch[1].trim();
      // Format the verification methods nicely
      const methods = verificationText.split(/\d+\.\s*/).filter(method => method.trim().length > 0);
      return methods.map((method, index) => `${index + 1}. ${method.trim()}`).join('\n');
    }

    // Fallback to simple verification pattern
    const simpleVerificationMatch = solutionText.match(/Verification:\s*(.*?)(?=Alternative|$)/is);
    return simpleVerificationMatch ? simpleVerificationMatch[1].trim() : '';
  }

  private assessDifficulty(question: string): 'basic' | 'intermediate' | 'advanced' {
    const basicKeywords = ['what is', 'define', 'basic', 'simple', 'introduction'];
    const advancedKeywords = ['derive', 'prove', 'complex', 'advanced', 'analysis', 'optimization'];

    const lowerQuestion = question.toLowerCase();

    if (basicKeywords.some(keyword => lowerQuestion.includes(keyword))) {
      return 'basic';
    } else if (advancedKeywords.some(keyword => lowerQuestion.includes(keyword))) {
      return 'advanced';
    }
    return 'intermediate';
  }

  private assessGATERelevance(question: string): number {
    const gateKeywords = [
      'gate', 'mechanical engineering', 'thermodynamics', 'fluid mechanics',
      'heat transfer', 'machine design', 'manufacturing', 'strength of materials',
      'engineering mechanics', 'theory of machines', 'vibrations'
    ];

    const lowerQuestion = question.toLowerCase();
    const relevantKeywords = gateKeywords.filter(keyword =>
      lowerQuestion.includes(keyword)
    );

    return Math.min(10, Math.max(1, relevantKeywords.length * 2));
  }

  private generateNumericalPrompt(question: string, filters: SearchFilters, conversationHistory: ChatMessage[] = []): string {
    // Build conversation context
    let contextString = '';
    if (conversationHistory.length > 0) {
      const recentHistory = conversationHistory.slice(-6); // Last 3 exchanges
      contextString = '\n\nConversation Context:\n';
      recentHistory.forEach((msg) => {
        if (msg.type === 'user') {
          contextString += `Student: ${msg.content}\n`;
        } else if (msg.type === 'ai' && msg.query?.response?.answer) {
          contextString += `AI: ${msg.query.response.answer}\n`;
        }
      });
    }

    const enhancedMode = filters.enhancedCalculations ? 'ENHANCED' : 'STANDARD';

    return `You are an expert GATE Mechanical Engineering numerical problem solver specializing in detailed step-by-step calculations.
${contextString}
Student Question: "${question}"

NUMERICAL PROBLEMS ONLY MODE - ${enhancedMode} CALCULATIONS

You MUST treat this as a numerical problem requiring detailed mathematical solution. Structure your response as follows:

**PROBLEM ANALYSIS:**
[Analyze what type of numerical problem this is and identify the key concepts involved]

**DIRECT ANSWER:**
[Provide the final numerical answer with proper units upfront]

**DETAILED EXPLANATION:**
[Brief explanation of the physical principles and approach]

**KEY POINTS:**
- [Key concept 1]
- [Key concept 2]
- [Key concept 3]
- [Key concept 4]

**FORMULAS:**
For each formula used, provide:
Formula Name: [Name]
Formula: $[Complete LaTeX notation]$
Description: [What the formula represents]
Units: [SI units]
Variables: [Define each variable with units]

**STEP-BY-STEP SOLUTION:**
Problem Type: numerical

Given Data:
- [Parameter 1]: [Value] [Unit] - [Description]
- [Parameter 2]: [Value] [Unit] - [Description]

To Find: [What needs to be calculated]

Assumptions:
- [Assumption 1]
- [Assumption 2]

Solution Approach: [Brief overview of calculation method]

Solution Steps:
Step 1: [Descriptive Title]
Description: [Detailed description of this calculation step]
Formula: $[Complete LaTeX formula]$
Substitution: [Show values being substituted]
$[variable_1] = [value_1] [unit_1]$
$[variable_2] = [value_2] [unit_2]$
Calculation: [Show complete mathematical work]
$[formula] = [substituted_values]$
$= [intermediate_calculation_1]$
$= [intermediate_calculation_2]$
$= [final_result] [units]$
Result: [Clear result with units and significant figures]
Explanation: [Why this step is necessary]

[Continue for all calculation steps...]

Final Answer: [Complete final answer with proper units, significant figures, and engineering notation if applicable]

Verification Methods:
1. [Dimensional Analysis]: [Check units consistency]
2. [Order of Magnitude]: [Verify result is reasonable]
3. [Alternative Method]: [Brief alternative calculation approach]
4. [Physical Reasonableness]: [Does the answer make physical sense?]

Alternative Solution Method: [Brief description of alternative approach if applicable]

**RELATED TOPICS:**
- [Related GATE ME topic 1]
- [Related GATE ME topic 2]
- [Related GATE ME topic 3]

CRITICAL REQUIREMENTS:
- Show EVERY mathematical step with actual numbers
- Use proper LaTeX notation for all equations: $F = ma$, $\sigma = \frac{F}{A}$
- Include units in every calculation step
- Show substitution of values clearly
- Provide multiple verification methods
- Use engineering notation for very large/small numbers
- Round final answers to appropriate significant figures
- Explain the physical meaning of the result

${filters.difficulty !== 'all' ? `Difficulty Level: ${filters.difficulty}` : ''}
${enhancedMode === 'ENHANCED' ? 'ENHANCED MODE: Provide extra detailed intermediate steps, multiple solution paths, and comprehensive error analysis.' : ''}`;
  }

  private getDefaultFilters(): SearchFilters {
    return {
      includeWebSearch: true,
      includeYouTube: true,
      includeImages: true,
      difficulty: 'all',
      contentType: 'all',
      numericalOnly: false,
      enhancedCalculations: false
    };
  }

  private getFallbackResponse(question: string, errorMessage?: string): AIResponse {
    return {
      id: `fallback_${Date.now()}`,
      answer: 'I apologize, but I encountered an error while processing your question.',
      explanation: errorMessage || 'Please try rephrasing your question or check your internet connection.',
      keyPoints: ['Try rephrasing your question', 'Check internet connection', 'Contact support if issue persists'],
      webSources: [],
      youtubeVideos: [],
      images: [],
      relatedTopics: [],
      difficulty: 'basic',
      gateRelevance: 1,
      timestamp: new Date().toISOString()
    };
  }

  // Cache management methods
  private generateCacheKey(question: string, filters: SearchFilters): string {
    return `${question.toLowerCase().trim()}_${JSON.stringify(filters)}`;
  }

  private getCachedResponse(cacheKey: string): AIResponse | null {
    const cached = this.responseCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.response;
    }
    if (cached) {
      this.responseCache.delete(cacheKey);
    }
    return null;
  }

  private cacheResponse(cacheKey: string, response: AIResponse): void {
    // Clean old cache entries if at max size
    if (this.responseCache.size >= this.MAX_CACHE_SIZE) {
      const oldestKey = this.responseCache.keys().next().value;
      if (oldestKey) {
        this.responseCache.delete(oldestKey);
      }
    }

    this.responseCache.set(cacheKey, {
      response,
      timestamp: Date.now()
    });
  }

  // Rate limiting methods
  private checkRateLimit(): boolean {
    const now = Date.now();
    const userKey = 'default'; // In a real app, use user ID
    const requests = this.rateLimitMap.get(userKey) || [];

    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < this.RATE_LIMIT_WINDOW);

    if (validRequests.length >= this.MAX_REQUESTS_PER_MINUTE) {
      return false;
    }

    validRequests.push(now);
    this.rateLimitMap.set(userKey, validRequests);
    return true;
  }

  // Timeout wrapper for API calls
  private withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    return Promise.race([
      promise,
      new Promise<T>((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), timeoutMs)
      )
    ]);
  }

  // Test function for image search configuration
  async testImageSearch(): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      if (!this.googleSearchApiKey) {
        return {
          success: false,
          message: 'Google Search API key not configured. Please add VITE_GOOGLE_SEARCH_API_KEY to your .env file.'
        };
      }

      if (!this.imageSearchEngineId) {
        return {
          success: false,
          message: 'Search Engine ID not configured. Please add VITE_GOOGLE_SEARCH_ENGINE_ID to your .env file.'
        };
      }

      // Test with a simple query
      const testImages = await this.searchImages('mechanical engineering');

      if (testImages.length > 0 && !testImages[0].url.includes('placeholder')) {
        return {
          success: true,
          message: `Image search working! Found ${testImages.length} images.`,
          details: { imageCount: testImages.length, firstImage: testImages[0] }
        };
      } else {
        return {
          success: false,
          message: 'Image search API configured but no real images returned. Check your Custom Search Engine setup.',
          details: { fallbackImages: testImages.length }
        };
      }
    } catch (error) {
      return {
        success: false,
        message: `Image search test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: { error }
      };
    }
  }
}

export const askAIService = new AskAIService();
